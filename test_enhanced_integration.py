"""
Test script for Enhanced Learning System Integration
Verifies that all components work together correctly
"""

import sys
from datetime import datetime

def test_imports():
    """Test that all required modules can be imported"""
    print("🔍 Testing imports...")
    
    try:
        from learning_system_integration import enhanced_gui_integration, learning_integrator
        print("✅ Enhanced learning system integration imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import enhanced learning system: {e}")
        return False
    
    try:
        from robust_validation_system import robust_validator
        print("✅ Robust validation system imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import validation system: {e}")
        return False
    
    try:
        from enhanced_adaptive_learning_v2 import enhanced_learning_system_v2
        print("✅ Enhanced adaptive learning system V2 imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import learning system V2: {e}")
        return False
    
    return True

def test_tournament_classification():
    """Test tournament classification functionality"""
    print("\n🏆 Testing tournament classification...")
    
    try:
        from learning_system_integration import learning_integrator
        
        # Test ATP classification
        result = learning_integrator.tournament_classifier.classify_tournament(
            tournament_name="Wimbledon Championships",
            player1_name="<PERSON>",
            player2_name="<PERSON>"
        )
        print(f"✅ Wimbledon classified as: {result.inferred_level} (confidence: {result.confidence:.2f})")
        
        # Test Challenger classification
        result = learning_integrator.tournament_classifier.classify_tournament(
            tournament_name="Challenger Biella",
            player1_name="Player A",
            player2_name="Player B"
        )
        print(f"✅ Challenger classified as: {result.inferred_level} (confidence: {result.confidence:.2f})")
        
        return True
    except Exception as e:
        print(f"❌ Tournament classification test failed: {e}")
        return False

def test_weight_retrieval():
    """Test weight retrieval for different tournament levels"""
    print("\n⚖️ Testing weight retrieval...")
    
    try:
        from learning_system_integration import enhanced_gui_integration
        
        # Test ATP weights
        result = enhanced_gui_integration['get_prediction_weights'](
            surface="Clay",
            tournament_name="Roland Garros",
            additional_context={'tournament_level': 'ATP', 'set_number': 1}
        )
        print(f"✅ ATP Clay weights retrieved: {result['tournament_level_used']}")
        print(f"   Sample weight: Service Consistency = {result['weights']['service_consistency_weight']:.3f}")
        
        # Test Challenger weights
        result = enhanced_gui_integration['get_prediction_weights'](
            surface="Hard",
            tournament_name="Challenger Event",
            additional_context={'tournament_level': 'Challenger', 'set_number': 2}
        )
        print(f"✅ Challenger Hard weights retrieved: {result['tournament_level_used']}")
        print(f"   Sample weight: Momentum Intensity = {result['weights']['momentum_intensity_weight']:.3f}")
        
        return True
    except Exception as e:
        print(f"❌ Weight retrieval test failed: {e}")
        return False

def test_learning_status():
    """Test learning system status retrieval"""
    print("\n📊 Testing learning system status...")
    
    try:
        from learning_system_integration import learning_integrator
        
        status = learning_integrator.get_integration_status()
        print(f"✅ Learning system status retrieved")
        print(f"   Total predictions: {status['total_predictions']}")
        print(f"   Segments with weights: {status['segments_with_weights']}")
        print(f"   Tournament classifier active: {status['tournament_classifier_active']}")
        
        return True
    except Exception as e:
        print(f"❌ Learning status test failed: {e}")
        return False

def test_validation_system():
    """Test validation system (if enough data available)"""
    print("\n🔬 Testing validation system...")
    
    try:
        from learning_system_integration import enhanced_gui_integration
        
        result = enhanced_gui_integration['run_validation']()
        
        if result.get('status') == 'insufficient_data':
            print(f"⚠️ Insufficient data for validation (need {result.get('minimum_required', 50)} predictions)")
            print(f"   Current predictions: {result.get('total_predictions', 0)}")
            return True  # This is expected for new installations
        else:
            print("✅ Validation system executed successfully")
            overall = result.get('validation_result', {}).get('overall_summary', {})
            print(f"   Reliability score: {overall.get('system_reliability_score', 0.0):.2f}")
            print(f"   Overall accuracy: {overall.get('overall_accuracy', 0.0):.3f}")
            return True
    except Exception as e:
        print(f"❌ Validation system test failed: {e}")
        return False

def test_dashboard_availability():
    """Test if dashboard can be launched"""
    print("\n🖥️ Testing dashboard availability...")
    
    try:
        import enhanced_learning_dashboard
        print("✅ Enhanced learning dashboard module available")
        return True
    except ImportError as e:
        print(f"❌ Dashboard not available: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Enhanced Learning System Integration Test")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_imports),
        ("Tournament Classification", test_tournament_classification),
        ("Weight Retrieval", test_weight_retrieval),
        ("Learning Status", test_learning_status),
        ("Validation System", test_validation_system),
        ("Dashboard Availability", test_dashboard_availability)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print("\n" + "="*50)
    print(f"📊 TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Enhanced learning system is ready to use.")
        print("\n📋 Next steps:")
        print("1. Start tennis.py")
        print("2. Select tournament level (ATP/Challenger/WTA) in Input Data")
        print("3. Make predictions and record outcomes")
        print("4. Use 'Learning Dashboard' button to monitor progress")
        print("5. Use 'Run Validation' button after 50+ predictions")
    else:
        print("⚠️ Some tests failed. Please check the error messages above.")
        print("Make sure all required files are in the same directory.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
