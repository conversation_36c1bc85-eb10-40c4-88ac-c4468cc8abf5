[{"timestamp": "2025-07-29T16:18:22.584295", "score": "3-3", "player1_name": "<PERSON><PERSON>", "player2_name": "<PERSON>", "player1_code": "UGO", "player2_code": "DAR", "predicted_winner": "UGO", "prediction_probability": 0.507315888, "confidence": 0.32938953280000005, "actual_winner": null, "momentum_factors": {"UGO": {"consecutive_015_starts": 2, "recent_three_point_runs": 3, "break_points_in_set": 0, "games_held_percentage": 1.0, "current_momentum": "strong_serving", "momentum_intensity": "MomentumIntensity(intensity_score=7.5920000000000005, duration_games=1, shift_trigger='three_point_run', trend_direction='stable', confidence=0.0)", "pressure_metrics": "PressureMetrics(service_pressure_index=0.39000000000000007, clutch_performance_rate=1.0, mental_fatigue_score=0.3, break_point_conversion_rate=0.0, pressure_response_pattern='clutch')", "momentum_history": "[7.970000000000001, 6.62, 7.5920000000000005]", "pressure_situations_faced": 1, "pressure_situations_won": 1, "serving_rhythm": "ServingRhythmMetrics(service_point_win_by_position={'15-0': 1.0, '30-0': 1.0, '40-0': 0.0, '0-15': 0.0, '0-30': 1.0, '15-30': 1.0, '30-30': 1.0, '40-30': 0.0, '40-40': 1.0, 'AD-40': 0.0}, recovery_from_0_15=1.0, recovery_from_0_30=1.0, recovery_from_0_40=0.0, closing_from_40_0=1.0, closing_from_40_15=0.0, closing_from_40_30=1.0, deuce_game_win_rate=1.0, avg_deuce_game_length=7.0, deuce_games_played=2, current_hold_streak=3, longest_hold_streak=3, break_vulnerability_windows=[], quick_holds=1, struggle_holds=0, avg_points_per_service_game=5.666666666666667, estimated_first_serve_effectiveness=0.3333333333333333, service_consistency_score=9.2)", "return_metrics": "ReturnGameMetrics(early_advantage_rate=0.3333333333333333, return_game_dominance_score=-1.0, aggressive_return_patterns=0, break_points_created=0, break_points_converted=0, break_point_conversion_rate=0.0, break_point_creation_rate=0.0, return_games_played=3, return_games_won=0, return_game_win_rate=0.0, return_game_pressure_applied=0, return_consistency_score=4.166666666666667, consecutive_return_game_wins=0, return_momentum_intensity=0.20000000000000004, return_pressure_situations=0, return_pressure_success_rate=0.0, return_game_tempo_preference='unknown', deuce_return_game_win_rate=0.0, clutch_return_performance=0.0, return_game_start_patterns={}, return_game_closing_efficiency={})"}, "DAR": {"consecutive_015_starts": 1, "recent_three_point_runs": 2, "break_points_in_set": 0, "games_held_percentage": 1.0, "current_momentum": "strong_serving", "momentum_intensity": "MomentumIntensity(intensity_score=7.8, duration_games=1, shift_trigger='three_point_run', trend_direction='stable', confidence=0.0)", "pressure_metrics": "PressureMetrics(service_pressure_index=0.0, clutch_performance_rate=0.5, mental_fatigue_score=0.0, break_point_conversion_rate=0.0, pressure_response_pattern='neutral')", "momentum_history": "[6.8, 7.970000000000001, 7.8]", "pressure_situations_faced": 0, "pressure_situations_won": 0, "serving_rhythm": "ServingRhythmMetrics(service_point_win_by_position={'15-0': 1.0, '30-0': 0.5, '30-15': 1.0, '40-15': 0.0, '40-0': 0.0, '0-15': 1.0, '15-15': 1.0}, recovery_from_0_15=1.0, recovery_from_0_30=0.0, recovery_from_0_40=0.0, closing_from_40_0=1.0, closing_from_40_15=1.0, closing_from_40_30=0.0, deuce_game_win_rate=0.0, avg_deuce_game_length=0.0, deuce_games_played=0, current_hold_streak=3, longest_hold_streak=3, break_vulnerability_windows=[], quick_holds=3, struggle_holds=0, avg_points_per_service_game=3.6666666666666665, estimated_first_serve_effectiveness=0.6666666666666666, service_consistency_score=7.2)", "return_metrics": "ReturnGameMetrics(early_advantage_rate=0.6666666666666666, return_game_dominance_score=1.3333333333333333, aggressive_return_patterns=0, break_points_created=0, break_points_converted=0, break_point_conversion_rate=0.0, break_point_creation_rate=0.0, return_games_played=3, return_games_won=0, return_game_win_rate=0.0, return_game_pressure_applied=2, return_consistency_score=6.0, consecutive_return_game_wins=0, return_momentum_intensity=2.733333333333334, return_pressure_situations=0, return_pressure_success_rate=0.0, return_game_tempo_preference='unknown', deuce_return_game_win_rate=0.0, clutch_return_performance=0.0, return_game_start_patterns={'advantage_2_1': 2}, return_game_closing_efficiency={})"}}, "set_number": 1, "match_format": "Bo3", "favorite": null, "favorite_odds": null, "surface": "<PERSON>", "previous_sets_winner": null, "is_ai_prediction": false, "ai_analysis_text": null, "ai_model_used": null, "prompt_weights": null, "context_factors": null, "learning_metadata": {"timestamp": "2025-07-29T16:18:22.584295", "gui_version": "enhanced_gui_v4", "tournament_level": "ATP", "tournament_name": "EFG Swiss Open Gstaad"}, "prompt_version": null, "weight_source": null, "session_id": null, "match_status": "pending", "prediction_id": null}, {"timestamp": "2025-07-29T16:18:27.548099", "score": "3-3", "player1_name": "<PERSON><PERSON>", "player2_name": "<PERSON>", "player1_code": "UGO", "player2_code": "DAR", "predicted_winner": "DAR", "prediction_probability": 0.515, "confidence": 1.0, "actual_winner": null, "momentum_factors": {"UGO": {"consecutive_015_starts": 2, "recent_three_point_runs": 3, "break_points_in_set": 0, "games_held_percentage": 1.0, "current_momentum": "strong_serving", "momentum_intensity": "MomentumIntensity(intensity_score=7.5920000000000005, duration_games=1, shift_trigger='three_point_run', trend_direction='stable', confidence=0.0)", "pressure_metrics": "PressureMetrics(service_pressure_index=0.39000000000000007, clutch_performance_rate=1.0, mental_fatigue_score=0.3, break_point_conversion_rate=0.0, pressure_response_pattern='clutch')", "momentum_history": "[7.970000000000001, 6.62, 7.5920000000000005]", "pressure_situations_faced": 1, "pressure_situations_won": 1, "serving_rhythm": "ServingRhythmMetrics(service_point_win_by_position={'15-0': 1.0, '30-0': 1.0, '40-0': 0.0, '0-15': 0.0, '0-30': 1.0, '15-30': 1.0, '30-30': 1.0, '40-30': 0.0, '40-40': 1.0, 'AD-40': 0.0}, recovery_from_0_15=1.0, recovery_from_0_30=1.0, recovery_from_0_40=0.0, closing_from_40_0=1.0, closing_from_40_15=0.0, closing_from_40_30=1.0, deuce_game_win_rate=1.0, avg_deuce_game_length=7.0, deuce_games_played=2, current_hold_streak=3, longest_hold_streak=3, break_vulnerability_windows=[], quick_holds=1, struggle_holds=0, avg_points_per_service_game=5.666666666666667, estimated_first_serve_effectiveness=0.3333333333333333, service_consistency_score=9.2)", "return_metrics": "ReturnGameMetrics(early_advantage_rate=0.3333333333333333, return_game_dominance_score=-1.0, aggressive_return_patterns=0, break_points_created=0, break_points_converted=0, break_point_conversion_rate=0.0, break_point_creation_rate=0.0, return_games_played=3, return_games_won=0, return_game_win_rate=0.0, return_game_pressure_applied=0, return_consistency_score=4.166666666666667, consecutive_return_game_wins=0, return_momentum_intensity=0.20000000000000004, return_pressure_situations=0, return_pressure_success_rate=0.0, return_game_tempo_preference='unknown', deuce_return_game_win_rate=0.0, clutch_return_performance=0.0, return_game_start_patterns={}, return_game_closing_efficiency={})"}, "DAR": {"consecutive_015_starts": 1, "recent_three_point_runs": 2, "break_points_in_set": 0, "games_held_percentage": 1.0, "current_momentum": "strong_serving", "momentum_intensity": "MomentumIntensity(intensity_score=7.8, duration_games=1, shift_trigger='three_point_run', trend_direction='stable', confidence=0.0)", "pressure_metrics": "PressureMetrics(service_pressure_index=0.0, clutch_performance_rate=0.5, mental_fatigue_score=0.0, break_point_conversion_rate=0.0, pressure_response_pattern='neutral')", "momentum_history": "[6.8, 7.970000000000001, 7.8]", "pressure_situations_faced": 0, "pressure_situations_won": 0, "serving_rhythm": "ServingRhythmMetrics(service_point_win_by_position={'15-0': 1.0, '30-0': 0.5, '30-15': 1.0, '40-15': 0.0, '40-0': 0.0, '0-15': 1.0, '15-15': 1.0}, recovery_from_0_15=1.0, recovery_from_0_30=0.0, recovery_from_0_40=0.0, closing_from_40_0=1.0, closing_from_40_15=1.0, closing_from_40_30=0.0, deuce_game_win_rate=0.0, avg_deuce_game_length=0.0, deuce_games_played=0, current_hold_streak=3, longest_hold_streak=3, break_vulnerability_windows=[], quick_holds=3, struggle_holds=0, avg_points_per_service_game=3.6666666666666665, estimated_first_serve_effectiveness=0.6666666666666666, service_consistency_score=7.2)", "return_metrics": "ReturnGameMetrics(early_advantage_rate=0.6666666666666666, return_game_dominance_score=1.3333333333333333, aggressive_return_patterns=0, break_points_created=0, break_points_converted=0, break_point_conversion_rate=0.0, break_point_creation_rate=0.0, return_games_played=3, return_games_won=0, return_game_win_rate=0.0, return_game_pressure_applied=2, return_consistency_score=6.0, consecutive_return_game_wins=0, return_momentum_intensity=2.733333333333334, return_pressure_situations=0, return_pressure_success_rate=0.0, return_game_tempo_preference='unknown', deuce_return_game_win_rate=0.0, clutch_return_performance=0.0, return_game_start_patterns={'advantage_2_1': 2}, return_game_closing_efficiency={})"}}, "set_number": 1, "match_format": "Bo3", "favorite": null, "favorite_odds": null, "surface": "<PERSON>", "previous_sets_winner": null, "is_ai_prediction": true, "ai_analysis_text": "<PERSON><PERSON>: 48.5%\n<PERSON>: 51.5%", "ai_model_used": "gemini-2.5-flash-enhanced", "prompt_weights": {"service_consistency_weight": 0.2725, "mental_fatigue_weight": 0.18750000000000003, "service_pressure_weight": 0.15, "momentum_intensity_weight": 0.2, "clutch_performance_weight": 0.05, "current_hold_streak_weight": 0.1, "deuce_game_performance_weight": 0.1, "context_multiplier": 1.0, "break_point_pressure": 1.0, "version": "1.0", "created_at": "2025-07-29T16:16:35.962712", "accuracy_score": 0.0, "sample_size": 0, "confidence_interval": [0.0, 0.0]}, "context_factors": {"surface": "<PERSON>", "set_number": 1, "score": [3, 3], "match_context": {"surface": "<PERSON>", "match_format": "Bo3", "set_number": 1, "favorite": null, "favorite_odds": null, "previous_sets_winner": null, "tournament_level": "ATP", "tournament_name": "EFG Swiss Open Gstaad"}}, "learning_metadata": {"timestamp": "2025-07-29T16:18:27.548099", "gui_version": "enhanced_gui_v4", "tournament_level": "ATP", "tournament_name": "EFG Swiss Open Gstaad"}, "prompt_version": "1.0", "weight_source": "adaptive_learning", "session_id": "default_session", "match_status": "pending", "prediction_id": "pred_20250729_161827_537982"}]