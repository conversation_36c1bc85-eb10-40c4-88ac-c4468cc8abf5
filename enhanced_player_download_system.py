"""
Enhanced Player Download System with Quality Validation and Smart Name Matching

This module provides an improved player downloading system that addresses:
1. Name mismatch issues between input names and downloaded file names
2. Data quality validation to ensure downloaded profiles contain sufficient data
3. Automatic retry mechanisms for failed downloads
4. Smart file naming and alias management
"""

import os
import re
import shutil
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from PyQt5.QtCore import QThread, pyqtSignal
from PyQt5.QtWidgets import QMessageBox

from player_data_quality_validator import data_quality_validator, ValidationResult
from player_data_parser import player_parser


class EnhancedPlayerDownloadWorker(QThread):
    """Enhanced worker thread with quality validation and smart naming"""
    
    progress_update = pyqtSignal(str)
    download_complete = pyqtSignal(str, bool, str, dict)  # player_name, success, message, validation_info
    
    def __init__(self, player_name: str, url: str = None, max_retries: int = 2):
        super().__init__()
        self.player_name = player_name
        self.url = url
        self.max_retries = max_retries
        self.should_stop = False
        self.retry_count = 0
    
    def run(self):
        """Enhanced download with quality validation and retry logic"""
        try:
            # Generate URL if not provided
            if not self.url:
                self.url = self.generate_tennis_abstract_url(self.player_name)
                self.progress_update.emit(f"🔗 Generated URL: {self.url}")
            
            # Attempt download with retries
            while self.retry_count <= self.max_retries and not self.should_stop:
                success, validation_info = self._attempt_download()
                
                if success:
                    self.download_complete.emit(
                        self.player_name, True, 
                        f"Successfully downloaded and validated {validation_info.get('saved_name', self.player_name)}",
                        validation_info
                    )
                    return
                
                self.retry_count += 1
                if self.retry_count <= self.max_retries:
                    self.progress_update.emit(f"🔄 Retry {self.retry_count}/{self.max_retries} for {self.player_name}...")
                    # Try alternative URL patterns on retry
                    self.url = self._generate_alternative_url(self.player_name, self.retry_count)
                    self.progress_update.emit(f"🔗 Trying alternative URL: {self.url}")
            
            # All retries failed
            self.download_complete.emit(
                self.player_name, False,
                f"Failed to download valid data for {self.player_name} after {self.max_retries + 1} attempts",
                {}
            )
            
        except Exception as e:
            self.download_complete.emit(
                self.player_name, False,
                f"Download error: {str(e)}",
                {}
            )
    
    def _attempt_download(self) -> Tuple[bool, Dict]:
        """Attempt a single download with validation"""
        try:
            self.progress_update.emit(f"📥 Downloading {self.player_name}...")
            
            # Import scraper
            try:
                from player_scraper import get_player_data, save_player_profile
            except ImportError:
                return False, {"error": "Scraper module not available"}
            
            # Download player data
            player_data = get_player_data(self.url)
            
            if self.should_stop:
                return False, {}
            
            if not player_data:
                return False, {"error": "No data retrieved from URL"}
            
            self.progress_update.emit(f"💾 Saving {self.player_name}...")
            
            # Save the profile
            success = save_player_profile(player_data, 'Players')
            
            if not success:
                return False, {"error": "Failed to save player profile"}
            
            # Get the actual saved file name
            saved_name = player_data.get('name', self.player_name).split('[')[0].strip()
            saved_file_path = Path('Players') / f"{saved_name}.txt"
            
            self.progress_update.emit(f"🔍 Validating data quality...")
            
            # Validate the downloaded file
            validation_result = data_quality_validator.validate_player_file(str(saved_file_path))
            
            validation_info = {
                'saved_name': saved_name,
                'saved_file': str(saved_file_path),
                'validation_result': validation_result,
                'input_name': self.player_name
            }
            
            if validation_result.is_valid:
                # Create name alias if needed
                self._create_name_alias_if_needed(self.player_name, saved_name)
                
                self.progress_update.emit(
                    f"✅ Quality validation passed (Score: {validation_result.quality_score:.1f}/100)"
                )
                return True, validation_info
            else:
                # Handle invalid data
                self.progress_update.emit(
                    f"⚠️ Quality validation failed (Score: {validation_result.quality_score:.1f}/100)"
                )
                
                # If file is too small or has critical issues, delete it and retry
                if (validation_result.file_size_bytes < data_quality_validator.MIN_FILE_SIZE_BYTES or 
                    not validation_result.essential_data_present):
                    
                    self.progress_update.emit(f"🗑️ Removing invalid file...")
                    saved_file_path.unlink(missing_ok=True)
                    return False, validation_info
                else:
                    # File has some issues but might be usable
                    self.progress_update.emit(f"⚠️ File saved with quality issues")
                    return True, validation_info
            
        except Exception as e:
            return False, {"error": str(e)}
    
    def _create_name_alias_if_needed(self, input_name: str, saved_name: str):
        """Create a name alias file if input name differs from saved name"""
        if input_name.lower().strip() != saved_name.lower().strip():
            # Create an alias file that points to the actual profile
            alias_file = Path('Players') / f"{input_name}.txt"
            actual_file = Path('Players') / f"{saved_name}.txt"
            
            if not alias_file.exists() and actual_file.exists():
                try:
                    # Create a symbolic link or copy
                    if os.name == 'nt':  # Windows
                        shutil.copy2(actual_file, alias_file)
                    else:  # Unix-like systems
                        alias_file.symlink_to(actual_file)
                    
                    self.progress_update.emit(f"🔗 Created alias: {input_name} → {saved_name}")
                except Exception as e:
                    self.progress_update.emit(f"⚠️ Could not create alias: {str(e)}")
    
    def _generate_alternative_url(self, player_name: str, retry_number: int) -> str:
        """Generate alternative URL patterns for retries"""
        base_url = "https://www.tennisabstract.com/cgi-bin/player-classic.cgi?p="
        
        if retry_number == 1:
            # Try without middle names
            name_parts = player_name.split()
            if len(name_parts) > 2:
                # Use only first and last name
                simplified_name = f"{name_parts[0]} {name_parts[-1]}"
                clean_name = re.sub(r'[^a-zA-Z\s]', '', simplified_name)
                url_name = ''.join(word.capitalize() for word in clean_name.split())
                return base_url + url_name
        
        elif retry_number == 2:
            # Try with different capitalization or spacing
            clean_name = re.sub(r'[^a-zA-Z\s]', '', player_name)
            # Try all lowercase with no spaces
            url_name = ''.join(clean_name.split()).lower()
            return base_url + url_name
        
        # Default fallback
        return self.generate_tennis_abstract_url(player_name)
    
    @staticmethod
    def generate_tennis_abstract_url(player_name: str) -> str:
        """Generate Tennis Abstract URL from player name"""
        clean_name = re.sub(r'[^a-zA-Z\s]', '', player_name)
        url_name = ''.join(word.capitalize() for word in clean_name.split())
        base_url = "https://www.tennisabstract.com/cgi-bin/player-classic.cgi?p="
        return base_url + url_name
    
    def stop(self):
        """Stop the download process"""
        self.should_stop = True


class EnhancedPlayerDownloadManager:
    """Manager for enhanced player downloads with quality validation"""
    
    def __init__(self):
        self.active_downloads = {}
        self.download_results = {}
    
    def download_missing_players(self, missing_players: List[str], progress_callback=None, 
                                completion_callback=None) -> Dict[str, bool]:
        """
        Download multiple players with enhanced validation
        
        Args:
            missing_players: List of player names to download
            progress_callback: Function to call with progress updates
            completion_callback: Function to call when all downloads complete
            
        Returns:
            Dictionary mapping player names to success status
        """
        results = {}
        
        for player_name in missing_players:
            if player_name not in self.active_downloads:
                worker = EnhancedPlayerDownloadWorker(player_name)
                
                if progress_callback:
                    worker.progress_update.connect(progress_callback)
                
                worker.download_complete.connect(
                    lambda name, success, msg, info: self._handle_download_complete(
                        name, success, msg, info, completion_callback
                    )
                )
                
                self.active_downloads[player_name] = worker
                worker.start()
        
        return results
    
    def _handle_download_complete(self, player_name: str, success: bool, message: str, 
                                 validation_info: Dict, completion_callback=None):
        """Handle completion of a download"""
        self.download_results[player_name] = {
            'success': success,
            'message': message,
            'validation_info': validation_info
        }
        
        # Remove from active downloads
        if player_name in self.active_downloads:
            del self.active_downloads[player_name]
        
        # Clear parser cache to pick up new files
        player_parser._player_cache.clear()
        
        if completion_callback:
            completion_callback(player_name, success, message, validation_info)
    
    def get_download_summary(self) -> Dict:
        """Get summary of all download results"""
        successful = [name for name, result in self.download_results.items() if result['success']]
        failed = [name for name, result in self.download_results.items() if not result['success']]
        
        return {
            'total': len(self.download_results),
            'successful': len(successful),
            'failed': len(failed),
            'successful_players': successful,
            'failed_players': failed,
            'results': self.download_results
        }
    
    def validate_existing_players(self, player_names: List[str]) -> Dict[str, ValidationResult]:
        """Validate quality of existing player files"""
        results = {}
        
        for player_name in player_names:
            # Try to find the player file
            player_file = player_parser._find_player_file(player_name)
            if player_file:
                validation_result = data_quality_validator.validate_player_file(str(player_file))
                results[player_name] = validation_result
            else:
                # Create a "not found" validation result
                results[player_name] = ValidationResult(
                    is_valid=False,
                    file_size_bytes=0,
                    quality_score=0.0,
                    issues=["Player file not found"],
                    warnings=[],
                    essential_data_present=False,
                    match_count=0
                )
        
        return results


# Global enhanced download manager
enhanced_download_manager = EnhancedPlayerDownloadManager()
