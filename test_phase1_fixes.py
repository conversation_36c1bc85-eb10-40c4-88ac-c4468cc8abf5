#!/usr/bin/env python3
"""
Test Phase 1 fixes for zeroed metrics issue
"""

import json
from enhanced_predictor import EnhancedTennisPredictor
from prediction_tracker import PredictionTracker

def test_enhanced_metrics_calculation():
    """Test the enhanced metrics calculation with historical fallbacks"""
    print("🧪 TESTING PHASE 1 ENHANCED METRICS CALCULATION")
    print("=" * 60)
    
    # Initialize predictor
    tracker = PredictionTracker()
    predictor = EnhancedTennisPredictor(tracker)
    
    # Test historical stats retrieval
    print("\n1. Testing Historical Stats Retrieval:")
    print("-" * 40)
    
    test_players = ["UGO", "DAR", "NOV", "RAF"]
    for player in test_players:
        stats = predictor._get_historical_player_stats(player)
        print(f"{player}: BP Conv: {stats['break_point_conversion_rate']:.1%}, "
              f"BP Save: {stats['break_point_save_rate']:.1%}, "
              f"Service Hold: {stats['service_hold_rate']:.1%}")
    
    # Test progressive break point conversion calculation
    print("\n2. Testing Progressive Break Point Conversion:")
    print("-" * 50)
    
    test_scenarios = [
        (0, 0, "No attempts"),
        (1, 1, "1/1 attempts"),
        (3, 2, "2/3 attempts"),
        (6, 4, "4/6 attempts"),
        (10, 7, "7/10 attempts")
    ]
    
    for attempts, conversions, description in test_scenarios:
        rate = predictor._calculate_progressive_bp_conversion_rate("UGO", attempts, conversions, "return")
        live_rate = f"{conversions/attempts:.1%}" if attempts > 0 else "N/A"
        print(f"{description}: {rate:.1%} (Live: {live_rate})")
    
    # Test progressive break point creation calculation
    print("\n3. Testing Progressive Break Point Creation:")
    print("-" * 48)
    
    creation_scenarios = [
        (0, 0, "No return games"),
        (2, 1, "1 BP in 2 return games"),
        (4, 3, "3 BP in 4 return games"),
        (8, 6, "6 BP in 8 return games")
    ]
    
    for bp_created, return_games, description in creation_scenarios:
        rate = predictor._calculate_progressive_bp_creation_rate("UGO", bp_created, return_games)
        live_rate = f"{bp_created/return_games:.2f}" if return_games > 0 else "N/A"
        print(f"{description}: {rate:.2f} BP/game (Live: {live_rate})")
    
    # Test enhanced mental fatigue calculation
    print("\n4. Testing Enhanced Mental Fatigue:")
    print("-" * 38)
    
    fatigue_scenarios = [
        (3, "Early set (3 games)"),
        (6, "Mid set (6 games)"),
        (10, "Late set (10 games)"),
        (15, "Very long set (15 games)")
    ]
    
    for games, description in fatigue_scenarios:
        fatigue = predictor._calculate_enhanced_mental_fatigue_score("UGO", games)
        print(f"{description}: {fatigue:.1%} fatigue")
    
    # Test enhanced clutch performance calculation
    print("\n5. Testing Enhanced Clutch Performance:")
    print("-" * 40)
    
    clutch_scenarios = [
        (0, 0, "No pressure situations"),
        (1, 1, "1/1 pressure situations"),
        (3, 2, "2/3 pressure situations"),
        (8, 6, "6/8 pressure situations")
    ]
    
    for faced, won, description in clutch_scenarios:
        clutch = predictor._calculate_enhanced_clutch_performance_rate("UGO", faced, won)
        live_rate = f"{won/faced:.1%}" if faced > 0 else "N/A"
        print(f"{description}: {clutch:.1%} (Live: {live_rate})")

def test_real_prediction_data():
    """Test with real prediction data to see improvements"""
    print("\n\n🔍 TESTING WITH REAL PREDICTION DATA")
    print("=" * 60)
    
    try:
        # Load recent prediction data
        with open('prediction_history.json', 'r') as f:
            predictions = json.load(f)
        
        if not predictions:
            print("No prediction data found")
            return
        
        # Analyze the most recent prediction
        recent_pred = predictions[-1]
        print(f"\nAnalyzing recent prediction:")
        print(f"Players: {recent_pred['player1_name']} vs {recent_pred['player2_name']}")
        print(f"Score: {recent_pred['score']}")
        print(f"Surface: {recent_pred.get('surface', 'Unknown')}")
        
        # Check momentum factors for zeroed values
        momentum_factors = recent_pred.get('momentum_factors', {})
        
        for player_code, factors in momentum_factors.items():
            print(f"\n{player_code} Metrics Analysis:")
            
            # Check for zeroed break point metrics
            pressure_metrics = factors.get('pressure_metrics', '')
            if 'break_point_conversion_rate=0.0' in pressure_metrics:
                print("  ❌ Break point conversion rate is zeroed")
            else:
                print("  ✅ Break point conversion rate has value")
            
            # Check return metrics
            return_metrics = factors.get('return_metrics', '')
            if 'break_point_conversion_rate=0.0' in return_metrics:
                print("  ❌ Return break point conversion rate is zeroed")
            else:
                print("  ✅ Return break point conversion rate has value")
            
            if 'break_point_creation_rate=0.0' in return_metrics:
                print("  ❌ Break point creation rate is zeroed")
            else:
                print("  ✅ Break point creation rate has value")
    
    except Exception as e:
        print(f"Error analyzing prediction data: {e}")

def main():
    """Run all tests"""
    test_enhanced_metrics_calculation()
    test_real_prediction_data()
    
    print("\n\n✅ PHASE 1 TESTING COMPLETED")
    print("=" * 60)
    print("Key improvements implemented:")
    print("• Historical fallbacks for break point metrics")
    print("• Progressive blending of live and historical data")
    print("• Enhanced mental fatigue calculation")
    print("• Improved clutch performance calculation")
    print("• Context-aware metric adjustments")

if __name__ == "__main__":
    main()
