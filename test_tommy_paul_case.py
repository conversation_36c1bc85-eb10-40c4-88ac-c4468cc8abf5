#!/usr/bin/env python3
"""
Test script for <PERSON> detection issue
"""

def test_tommy_paul_case():
    """Test the specific <PERSON> case that failed"""
    
    # <PERSON> data - PAU should be detected as starting server
    paul_data = """0
-
0
tied
SIN
PAU
15
0
15
15
15
30
15
40
1
-
0
<PERSON>IN
PAU
15
0
15
15
15
30
30
30
30
BP
40
40
40
40
BP
AD"""
    
    print("🧪 Testing Tommy Paul Detection Issue")
    print("=" * 50)
    print("Expected: <PERSON> (PAU) should be detected as starting server")
    print("Reason: <PERSON> won first game (1-0 <PERSON>) with no BP")
    print("First game score progression: 15-0, 15-15, 15-30, 15-40 (<PERSON> wins)")
    print()
    
    result = analyze_paul_data(paul_data, "SIN", "PAU")
    print(f"Result: {result}")
    
    print("\n" + "=" * 50)
    if result.get('starting_server') == 'PAU':
        print("🎯 Test Result: ✅ PASS - Correctly detected <PERSON> as starting server")
    else:
        print("🎯 Test Result: ❌ FAIL - Did not detect <PERSON> correctly")
        print(f"   Expected: PAU, Got: {result.get('starting_server')}")

def analyze_paul_data(match_data: str, player1_code: str, player2_code: str) -> dict:
    """Analyze Tommy Paul data using current logic"""
    try:
        lines = match_data.strip().split('\n')
        
        # Find first game data
        first_game_data = extract_first_game_debug(lines, player1_code, player2_code)
        if not first_game_data:
            return {"error": "No first game found"}
        
        first_game_winner = first_game_data['winner']
        has_break_point = first_game_data['has_bp']
        winner_name = first_game_data.get('winner_name', '')
        
        print(f"🔍 First game winner: {first_game_winner}")
        print(f"🔍 Winner name: '{winner_name}'")
        print(f"🔍 Has BP in first game: {has_break_point}")
        print(f"🔍 Game data lines: {first_game_data['game_data']}")
        print(f"🔍 Player codes: {player1_code}, {player2_code}")
        
        # Apply Break Point Theory
        if has_break_point:
            # BP present: Winner broke serve → Other player started serving
            if first_game_winner == player1_code:
                starting_server = player2_code
                logic = f"BP detected. {player1_code} broke {player2_code}'s serve → {player2_code} started serving"
            else:
                starting_server = player1_code
                logic = f"BP detected. {player2_code} broke {player1_code}'s serve → {player1_code} started serving"
        else:
            # No BP: Winner held serve → That player started serving
            starting_server = first_game_winner
            logic = f"No BP. {first_game_winner} held serve → {first_game_winner} started serving"
        
        return {
            "starting_server": starting_server,
            "logic": logic,
            "first_game_winner": first_game_winner,
            "winner_name": winner_name,
            "has_bp": has_break_point,
            "game_data": first_game_data['game_data']
        }
        
    except Exception as e:
        return {"error": str(e)}

def extract_first_game_debug(lines: list, player1_code: str, player2_code: str) -> dict:
    """Debug version of first game extraction"""
    try:
        print(f"🔍 Looking for first game in {len(lines)} lines")
        print(f"🔍 Player codes: {player1_code} = Player1, {player2_code} = Player2")
        
        i = 0
        while i < len(lines):
            line = lines[i].strip()
            
            # Skip empty lines
            if not line:
                i += 1
                continue
            
            print(f"🔍 Line {i}: '{line}'")
            
            # Look for first game pattern: "1-0" or "0-1"
            if (i + 2 < len(lines) and 
                lines[i+1].strip() == '-' and 
                lines[i].strip().isdigit() and 
                lines[i+2].strip().isdigit()):
                
                score1 = int(lines[i].strip())
                score2 = int(lines[i+2].strip())
                
                print(f"🔍 Found score: {score1}-{score2}")
                
                # Check if this is the first game (1-0 or 0-1)
                if (score1 == 1 and score2 == 0) or (score1 == 0 and score2 == 1):
                    print(f"🔍 This is the first game!")
                    # Found first game
                    i += 3  # Move past the score
                    
                    # Get winner name if present
                    winner_name = None
                    if i < len(lines) and any(char.isalpha() for char in lines[i]) and ' ' in lines[i]:
                        winner_name = lines[i].strip()
                        print(f"🔍 Winner name found: '{winner_name}'")
                        i += 1
                    
                    # Extract game data - go back to find point-by-point data
                    game_data_lines = []
                    
                    # Go back to find the start of this game (look for "tied")
                    game_start = i - 4 if winner_name else i - 3
                    while game_start >= 0:
                        if lines[game_start].strip().lower() == 'tied':
                            print(f"🔍 Found 'tied' at line {game_start}")
                            break
                        game_start -= 1
                    
                    # Extract all lines from after "tied" until the score
                    if game_start >= 0:
                        score_start = i - 4 if winner_name else i - 3
                        print(f"🔍 Extracting game data from line {game_start + 1} to {score_start}")
                        for j in range(game_start + 1, score_start):
                            if j < len(lines):
                                line = lines[j].strip()
                                # Skip player codes but include all other data
                                if line and not (len(line) == 3 and line.isupper()):
                                    game_data_lines.append(line)
                                    print(f"🔍 Added game data: '{line}'")
                    
                    # Determine winner using improved logic
                    winner_code = None
                    
                    print(f"🔍 Attempting to match winner name '{winner_name}' to player codes")
                    
                    # Try to match winner name to player codes
                    if winner_name:
                        # Check for Tommy Paul / PAU
                        if (player1_code and player1_code in winner_name) or \
                           ('Paul' in winner_name and player1_code == 'PAU') or \
                           ('Tommy' in winner_name and player1_code == 'PAU'):
                            winner_code = player1_code
                            print(f"🔍 Matched to player1: {player1_code}")
                        elif (player2_code and player2_code in winner_name) or \
                             ('Paul' in winner_name and player2_code == 'PAU') or \
                             ('Tommy' in winner_name and player2_code == 'PAU'):
                            winner_code = player2_code
                            print(f"🔍 Matched to player2: {player2_code}")
                        # Check for Sinner / SIN
                        elif ('Sinner' in winner_name and player1_code == 'SIN'):
                            winner_code = player1_code
                            print(f"🔍 Matched Sinner to player1: {player1_code}")
                        elif ('Sinner' in winner_name and player2_code == 'SIN'):
                            winner_code = player2_code
                            print(f"🔍 Matched Sinner to player2: {player2_code}")
                    
                    # Fallback to score position if name matching failed
                    if not winner_code:
                        if score1 == 1 and score2 == 0:
                            winner_code = player1_code
                            print(f"🔍 Fallback: score 1-0 → player1: {player1_code}")
                        else:
                            winner_code = player2_code
                            print(f"🔍 Fallback: score 0-1 → player2: {player2_code}")
                    
                    # Check for Break Point (BP) in FIRST GAME ONLY
                    has_bp = any('BP' in game_line for game_line in game_data_lines)
                    print(f"🔍 BP check result: {has_bp}")
                    
                    return {
                        'winner': winner_code,
                        'has_bp': has_bp,
                        'winner_name': winner_name,
                        'game_data': game_data_lines
                    }
            
            i += 1
        
        print("🔍 No first game pattern found")
        return None
        
    except Exception as e:
        print(f"Error in debug extraction: {e}")
        return None

if __name__ == "__main__":
    test_tommy_paul_case()
