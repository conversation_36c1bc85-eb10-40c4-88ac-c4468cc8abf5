#!/usr/bin/env python3
"""
Improved Player Downloader with better error handling and dependency checking
"""

import os
import sys
import subprocess
from pathlib import Path
from typing import Op<PERSON>, Tuple, List
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QLineEdit, QTextEdit, QProgressBar,
                            QMessageBox, QTabWidget, QWidget, QCheckBox)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont

from player_data_parser import player_parser


def check_dependencies():
    """Check if all required dependencies are available"""
    missing_deps = []
    
    # Check selenium
    try:
        import selenium
        print(f"✅ Selenium version: {selenium.__version__}")
    except ImportError:
        missing_deps.append("selenium")
    
    # Check beautifulsoup4
    try:
        import bs4
        print(f"✅ BeautifulSoup4 available")
    except ImportError:
        missing_deps.append("beautifulsoup4")
    
    # Check requests
    try:
        import requests
        print(f"✅ Requests available")
    except ImportError:
        missing_deps.append("requests")
    
    # Check Chrome WebDriver
    chrome_driver_available = False
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        
        # Try to create a Chrome driver instance
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        
        driver = webdriver.Chrome(options=chrome_options)
        driver.quit()
        chrome_driver_available = True
        print("✅ Chrome WebDriver available")
        
    except Exception as e:
        print(f"❌ Chrome WebDriver issue: {e}")
        missing_deps.append("chrome-webdriver")
    
    return missing_deps, chrome_driver_available


class ImprovedDownloadWorker(QThread):
    """Improved worker with better error handling"""
    
    progress_update = pyqtSignal(str)
    download_complete = pyqtSignal(str, bool, str)
    
    def __init__(self, player_name: str, url: str):
        super().__init__()
        self.player_name = player_name
        self.url = url
        self.should_stop = False
    
    def run(self):
        """Download with improved error handling"""
        try:
            self.progress_update.emit(f"🔍 Checking dependencies...")
            
            # Check dependencies first
            missing_deps, chrome_available = check_dependencies()
            
            if missing_deps:
                error_msg = f"Missing dependencies: {', '.join(missing_deps)}\n"
                error_msg += "Please install:\n"
                for dep in missing_deps:
                    if dep == "selenium":
                        error_msg += "• pip install selenium\n"
                    elif dep == "beautifulsoup4":
                        error_msg += "• pip install beautifulsoup4\n"
                    elif dep == "requests":
                        error_msg += "• pip install requests\n"
                    elif dep == "chrome-webdriver":
                        error_msg += "• Download Chrome WebDriver from https://chromedriver.chromium.org/\n"
                        error_msg += "• Add to system PATH or place in project folder\n"
                
                self.download_complete.emit(self.player_name, False, error_msg)
                return
            
            self.progress_update.emit(f"📥 Downloading {self.player_name} from {self.url}")
            
            # Import and use the scraper
            try:
                from player_scraper import get_player_data, save_player_profile
                
                player_data = get_player_data(self.url)
                
                if self.should_stop:
                    return
                
                if player_data:
                    self.progress_update.emit(f"💾 Saving {self.player_name}...")
                    success = save_player_profile(player_data, 'Players')
                    
                    if success:
                        self.download_complete.emit(
                            self.player_name, True,
                            f"Successfully downloaded {player_data.get('name', self.player_name)}"
                        )
                    else:
                        self.download_complete.emit(
                            self.player_name, False,
                            "Downloaded data but failed to save profile"
                        )
                else:
                    self.download_complete.emit(
                        self.player_name, False,
                        f"No data retrieved from {self.url}. Please check if the URL is correct."
                    )
                    
            except ImportError as e:
                self.download_complete.emit(
                    self.player_name, False,
                    f"player_scraper.py not found or has import errors: {e}"
                )
            except Exception as e:
                self.download_complete.emit(
                    self.player_name, False,
                    f"Scraping error: {str(e)}"
                )
                
        except Exception as e:
            self.download_complete.emit(
                self.player_name, False,
                f"Unexpected error: {str(e)}"
            )
    
    def stop(self):
        self.should_stop = True


class DiagnosticDialog(QDialog):
    """Dialog to show system diagnostics and setup help"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.run_diagnostics()
    
    def setup_ui(self):
        self.setWindowTitle("System Diagnostics")
        self.setModal(True)
        self.setMinimumSize(600, 500)
        
        layout = QVBoxLayout()
        
        # Header
        header = QLabel("🔧 System Diagnostics & Setup")
        header.setFont(QFont("", 14, QFont.Bold))
        header.setAlignment(Qt.AlignCenter)
        layout.addWidget(header)
        
        # Diagnostics text
        self.diagnostics_text = QTextEdit()
        self.diagnostics_text.setReadOnly(True)
        layout.addWidget(self.diagnostics_text)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.install_button = QPushButton("📦 Install Dependencies")
        self.install_button.clicked.connect(self.install_dependencies)
        button_layout.addWidget(self.install_button)
        
        self.close_button = QPushButton("✅ Close")
        self.close_button.clicked.connect(self.accept)
        button_layout.addWidget(self.close_button)
        
        layout.addLayout(button_layout)
        self.setLayout(layout)
    
    def run_diagnostics(self):
        """Run system diagnostics"""
        self.diagnostics_text.append("🔍 Running system diagnostics...\n")
        
        # Check Python version
        python_version = sys.version
        self.diagnostics_text.append(f"🐍 Python Version: {python_version}\n")
        
        # Check dependencies
        missing_deps, chrome_available = check_dependencies()
        
        if not missing_deps:
            self.diagnostics_text.append("✅ All dependencies are available!\n")
        else:
            self.diagnostics_text.append("❌ Missing dependencies detected:\n")
            for dep in missing_deps:
                self.diagnostics_text.append(f"   • {dep}\n")
        
        # Installation instructions
        self.diagnostics_text.append("\n📋 INSTALLATION INSTRUCTIONS:\n")
        self.diagnostics_text.append("=" * 40 + "\n")
        
        if "selenium" in missing_deps:
            self.diagnostics_text.append("1. Install Selenium:\n")
            self.diagnostics_text.append("   pip install selenium\n\n")
        
        if "beautifulsoup4" in missing_deps:
            self.diagnostics_text.append("2. Install BeautifulSoup4:\n")
            self.diagnostics_text.append("   pip install beautifulsoup4\n\n")
        
        if "requests" in missing_deps:
            self.diagnostics_text.append("3. Install Requests:\n")
            self.diagnostics_text.append("   pip install requests\n\n")
        
        if "chrome-webdriver" in missing_deps:
            self.diagnostics_text.append("4. Install Chrome WebDriver:\n")
            self.diagnostics_text.append("   a) Download from: https://chromedriver.chromium.org/\n")
            self.diagnostics_text.append("   b) Match your Chrome browser version\n")
            self.diagnostics_text.append("   c) Extract and add to system PATH\n")
            self.diagnostics_text.append("   d) Or place chromedriver.exe in this project folder\n\n")
        
        # Quick fix suggestions
        self.diagnostics_text.append("🚀 QUICK FIX:\n")
        self.diagnostics_text.append("=" * 40 + "\n")
        self.diagnostics_text.append("Run these commands in your terminal:\n")
        self.diagnostics_text.append("pip install selenium beautifulsoup4 requests\n\n")
        
        self.diagnostics_text.append("For Chrome WebDriver:\n")
        self.diagnostics_text.append("1. Check your Chrome version: chrome://version/\n")
        self.diagnostics_text.append("2. Download matching WebDriver\n")
        self.diagnostics_text.append("3. Add to PATH or project folder\n")
    
    def install_dependencies(self):
        """Attempt to install Python dependencies"""
        self.diagnostics_text.append("\n🔄 Attempting to install dependencies...\n")
        
        try:
            # Install Python packages
            packages = ["selenium", "beautifulsoup4", "requests"]
            for package in packages:
                self.diagnostics_text.append(f"Installing {package}...\n")
                result = subprocess.run([sys.executable, "-m", "pip", "install", package], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    self.diagnostics_text.append(f"✅ {package} installed successfully\n")
                else:
                    self.diagnostics_text.append(f"❌ Failed to install {package}: {result.stderr}\n")
            
            self.diagnostics_text.append("\n⚠️ Note: Chrome WebDriver must be installed manually\n")
            self.diagnostics_text.append("Please download from: https://chromedriver.chromium.org/\n")
            
        except Exception as e:
            self.diagnostics_text.append(f"❌ Installation error: {e}\n")


class ImprovedDownloadDialog(QDialog):
    """Improved download dialog with diagnostics"""
    
    def __init__(self, missing_players: List[str], parent=None):
        super().__init__(parent)
        self.missing_players = missing_players
        self.download_workers = {}
        self.download_results = {}
        self.setup_ui()
    
    def setup_ui(self):
        self.setWindowTitle("Download Player Profiles")
        self.setModal(True)
        self.setMinimumSize(700, 600)
        
        layout = QVBoxLayout()
        
        # Header
        header = QLabel("🚀 Player Profile Download")
        header.setFont(QFont("", 14, QFont.Bold))
        header.setAlignment(Qt.AlignCenter)
        layout.addWidget(header)
        
        # Missing players info
        info_text = f"Missing player profiles:\n"
        for i, player in enumerate(self.missing_players, 1):
            info_text += f"{i}. {player}\n"
        
        info_label = QLabel(info_text)
        info_label.setStyleSheet("padding: 10px; background-color: #f0f0f0; border-radius: 5px;")
        layout.addWidget(info_label)
        
        # Diagnostics button
        diag_button = QPushButton("🔧 Check System Dependencies")
        diag_button.clicked.connect(self.show_diagnostics)
        layout.addWidget(diag_button)
        
        # Manual URL inputs
        layout.addWidget(QLabel("🔗 Manual URL Input:"))
        
        self.url_inputs = {}
        for player in self.missing_players:
            player_layout = QHBoxLayout()
            
            label = QLabel(f"{player}:")
            label.setMinimumWidth(150)
            player_layout.addWidget(label)
            
            url_input = QLineEdit()
            url_input.setPlaceholderText(f"https://www.tennisabstract.com/cgi-bin/player-classic.cgi?p={player.replace(' ', '')}")
            player_layout.addWidget(url_input)
            
            download_btn = QPushButton("📥 Download")
            download_btn.clicked.connect(lambda checked, p=player, inp=url_input: self.download_player(p, inp.text()))
            player_layout.addWidget(download_btn)
            
            layout.addLayout(player_layout)
            self.url_inputs[player] = url_input
        
        # Status area
        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(150)
        self.status_text.setReadOnly(True)
        layout.addWidget(self.status_text)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # Action buttons
        button_layout = QHBoxLayout()
        
        self.continue_button = QPushButton("✅ Continue with Analysis")
        self.continue_button.clicked.connect(self.accept)
        button_layout.addWidget(self.continue_button)
        
        self.skip_button = QPushButton("⏭️ Skip Download")
        self.skip_button.clicked.connect(self.accept)
        button_layout.addWidget(self.skip_button)
        
        self.cancel_button = QPushButton("❌ Cancel")
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)
        
        layout.addLayout(button_layout)
        self.setLayout(layout)
    
    def show_diagnostics(self):
        """Show diagnostics dialog"""
        diag_dialog = DiagnosticDialog(self)
        diag_dialog.exec_()
    
    def download_player(self, player_name: str, url: str):
        """Download a player with improved error handling"""
        if not url.strip():
            QMessageBox.warning(self, "Missing URL", f"Please enter a URL for {player_name}")
            return
        
        if player_name in self.download_workers:
            return  # Already downloading
        
        self.status_text.append(f"🚀 Starting download for {player_name}...")
        
        worker = ImprovedDownloadWorker(player_name, url.strip())
        worker.progress_update.connect(self.update_progress)
        worker.download_complete.connect(self.download_finished)
        
        self.download_workers[player_name] = worker
        worker.start()
    
    def update_progress(self, message: str):
        """Update progress display"""
        self.status_text.append(message)
        self.status_text.verticalScrollBar().setValue(
            self.status_text.verticalScrollBar().maximum()
        )
    
    def download_finished(self, player_name: str, success: bool, message: str):
        """Handle download completion"""
        self.download_results[player_name] = success
        
        if success:
            self.status_text.append(f"✅ {message}")
            player_parser._player_cache.clear()  # Clear cache
        else:
            self.status_text.append(f"❌ {message}")
        
        # Update continue button
        successful = sum(1 for s in self.download_results.values() if s)
        if successful > 0:
            self.continue_button.setText(f"✅ Continue with {successful} New Player(s)")


def show_improved_download_dialog(missing_players: List[str], parent=None) -> bool:
    """Show the improved download dialog"""
    dialog = ImprovedDownloadDialog(missing_players, parent)
    result = dialog.exec_()
    return result == QDialog.Accepted
