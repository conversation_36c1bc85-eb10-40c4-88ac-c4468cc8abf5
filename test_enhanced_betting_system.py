#!/usr/bin/env python3
"""
Test script for Enhanced Betting Recommendations System
"""

import sys
import os
from datetime import datetime

def test_enhanced_betting_system():
    """Test the enhanced betting system functionality"""

    print("🎾 Testing Enhanced Betting Recommendations System")
    print("=" * 60)
    print("Note: Testing with clean system (no historical data)")
    print("=" * 60)
    
    try:
        # Import the enhanced betting system
        from money_making_betting_system import MoneyMakingBettingSystem
        
        # Initialize the system
        print("1. Initializing Enhanced Betting System...")
        system = MoneyMakingBettingSystem(starting_bankroll=1000)
        
        # Test learning system connections
        print("\n2. Testing Learning System Connections...")
        status = system.get_learning_system_status()
        print(f"   Enhanced Learning Connected: {status['enhanced_learning_connected']}")
        print(f"   Adaptive Learning Connected: {status['adaptive_learning_connected']}")
        print(f"   Available Data Sources: {', '.join(status['data_sources_available'])}")
        
        # Test betting opportunity evaluation
        print("\n3. Testing Betting Opportunity Evaluation...")
        
        test_scenarios = [
            {
                'score': '3-3',
                'set_number': 2,
                'predicted_player': 'Test Player A',
                'confidence': 0.65,
                'momentum_type': 'strong_serving',
                'surface': 'Clay'
            },
            {
                'score': '6-6',
                'set_number': 3,
                'predicted_player': 'Test Player B',
                'confidence': 0.75,
                'momentum_type': 'solid_serving',
                'surface': 'Hard'
            },
            {
                'score': '5-5',
                'set_number': 1,
                'predicted_player': 'Test Player C',
                'confidence': 0.55,
                'momentum_type': 'weak_serving',
                'surface': 'Grass'
            }
        ]
        
        for i, scenario in enumerate(test_scenarios, 1):
            print(f"\n   Scenario {i}: {scenario['score']} Set {scenario['set_number']} on {scenario['surface']}")
            
            opportunity = system.evaluate_betting_opportunity(
                score=scenario['score'],
                set_number=scenario['set_number'],
                predicted_player=scenario['predicted_player'],
                confidence=scenario['confidence'],
                momentum_type=scenario['momentum_type'],
                surface=scenario['surface']
            )
            
            print(f"   Recommendation Tier: {opportunity.recommendation_tier}")
            print(f"   Stake Percentage: {opportunity.bet_size_percentage * 100:.1f}%")
            print(f"   Expected ROI: {opportunity.expected_roi:.1f}%")
            print(f"   Risk Level: {opportunity.risk_level}")
            print(f"   AI Accuracy: {opportunity.ai_accuracy:.1f}%")
            print(f"   Sample Size: {opportunity.sample_size}")
            print(f"   Reasoning: {opportunity.reasoning}")
        
        # Test performance summary
        print("\n4. Testing Performance Summary...")
        summary = system.get_performance_summary()
        print(f"   Current Bankroll: ${summary['recommendation_stats']['current_bankroll']:.2f}")
        print(f"   Bankroll Change: {summary['recommendation_stats']['bankroll_change_percentage']:.1f}%")
        print(f"   Opportunities Evaluated: {summary['recommendation_stats']['total_opportunities_evaluated']}")
        
        # Test integration with enhanced GUI format
        print("\n5. Testing Enhanced GUI Integration...")
        prediction_data = {
            'current_score': '4-4',
            'current_set': 2,
            'predicted_winner': 'Integration Test Player',
            'confidence': 0.68,
            'surface': 'Hard',
            'momentum_analysis': 'strong_serving momentum detected'
        }
        
        integrated_opportunity = system.integrate_with_enhanced_gui(prediction_data)
        print(f"   Integration Test - Tier: {integrated_opportunity.recommendation_tier}")
        print(f"   Integration Test - Stake: {integrated_opportunity.bet_size_percentage * 100:.1f}%")
        print(f"   Integration Test - Momentum: {integrated_opportunity.momentum_type}")
        
        print("\n✅ All tests completed successfully!")
        print("\n📊 Enhanced Betting System Status:")
        print(f"   - Learning Systems: {'Connected' if status['enhanced_learning_connected'] or status['adaptive_learning_connected'] else 'Using Fallback Data'}")
        print(f"   - Data Sources: {len(status['data_sources_available'])} available")
        print(f"   - System Ready: ✅ Yes")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("   Make sure money_making_betting_system.py is in the current directory")
        return False
        
    except Exception as e:
        print(f"❌ Test Error: {e}")
        print(f"   Error Type: {type(e).__name__}")
        return False

def test_betting_display_format():
    """Test the betting display format that appears in enhanced_gui"""
    
    print("\n" + "=" * 60)
    print("🎾 Testing Betting Display Format")
    print("=" * 60)
    
    try:
        from money_making_betting_system import MoneyMakingBettingSystem
        
        system = MoneyMakingBettingSystem()
        
        # Test a premium betting scenario
        opportunity = system.evaluate_betting_opportunity(
            score='6-6',
            set_number=3,
            predicted_player='Display Test Player',
            confidence=0.75,
            momentum_type='strong_serving',
            surface='Clay'
        )
        
        # Simulate the display format from tennis.py
        stake_percentage = opportunity.bet_size_percentage * 100
        
        print("\nSample Enhanced Betting Analysis Display:")
        print("--- 💰 ENHANCED BETTING ANALYSIS ---")
        
        tier_emojis = {
            'TIER_1_PREMIUM': '🏆',
            'TIER_2_STRONG': '⭐',
            'TIER_3_MODERATE': '🟡',
            'TIER_4_WEAK': '🟢',
            'AVOID': '🔴'
        }
        
        tier_emoji = tier_emojis.get(opportunity.recommendation_tier, '❓')
        
        if stake_percentage > 0:
            if opportunity.recommendation_tier == 'TIER_1_PREMIUM':
                print(f"• {tier_emoji} PREMIUM BET: {stake_percentage:.1f}% stake")
            elif opportunity.recommendation_tier == 'TIER_2_STRONG':
                print(f"• {tier_emoji} STRONG BET: {stake_percentage:.1f}% stake")
            elif opportunity.recommendation_tier == 'TIER_3_MODERATE':
                print(f"• {tier_emoji} MODERATE BET: {stake_percentage:.1f}% stake")
            elif opportunity.recommendation_tier == 'TIER_4_WEAK':
                print(f"• {tier_emoji} SMALL BET: {stake_percentage:.1f}% stake")
            elif opportunity.recommendation_tier == 'AVOID_NO_DATA':
                print(f"• {tier_emoji} NO DATA: System reset - need predictions first")
            else:
                print(f"• {tier_emoji} AVOID: Negative expected value")

            print(f"• 📊 Expected ROI: {opportunity.expected_roi:.1f}%")
            print(f"• ⚖️ Risk Level: {opportunity.risk_level.replace('_', ' ').title()}")
            print(f"• 🎯 AI Accuracy: {opportunity.ai_accuracy:.1f}%")

            if opportunity.sample_size > 0:
                print(f"• 📈 Sample Size: {opportunity.sample_size} matches")
        else:
            if opportunity.recommendation_tier == 'AVOID_NO_DATA':
                print(f"• {tier_emoji} RECOMMENDATION: COLLECT DATA FIRST")
                print(f"• 📊 Expected ROI: Cannot calculate without data")
                print(f"• ⚠️ Reason: System reset - make AI predictions to build data")
            else:
                print(f"• 🚫 RECOMMENDATION: SKIP THIS BET")
                print(f"• 📊 Expected ROI: {opportunity.expected_roi:.1f}%")
        
        print(f"• 💡 Analysis: {opportunity.reasoning}")
        
        print("\n--- 🔄 ADVANCED RE-BETTING STRATEGY ---")
        print("• 🎾 TIEBREAK ALERT: Maximum variance scenario")
        print("• 📉 Reduce position size by 30-50%")
        print("• 👀 Monitor first serve % closely")
        print("• ⚖️ Consider live hedging opportunities")
        
        print("\n--- 💡 BETTING TIPS ---")
        print("• ✅ Strong AI confidence - favorable scenario")
        print("• 🤖 Using latest AI learning data")
        
        print("\n✅ Display format test completed!")
        
        return True
        
    except Exception as e:
        print(f"❌ Display test error: {e}")
        return False

if __name__ == "__main__":
    print(f"Enhanced Betting System Test - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Run main functionality test
    main_test_passed = test_enhanced_betting_system()
    
    # Run display format test
    display_test_passed = test_betting_display_format()
    
    print("\n" + "=" * 60)
    print("🎾 TEST SUMMARY")
    print("=" * 60)
    print(f"Main Functionality Test: {'✅ PASSED' if main_test_passed else '❌ FAILED'}")
    print(f"Display Format Test: {'✅ PASSED' if display_test_passed else '❌ FAILED'}")
    
    if main_test_passed and display_test_passed:
        print("\n🎉 All tests passed! Enhanced Betting System is ready to use.")
        print("\n📋 Next Steps:")
        print("1. Open tennis.py (your main enhanced GUI)")
        print("2. Analyze any tennis match with tied scores")
        print("3. Check the 'Detailed Analysis' section for enhanced betting recommendations")
        print("4. Follow the tier-based betting guidance")
    else:
        print("\n⚠️ Some tests failed. Please check the error messages above.")
        
    print("\n" + "=" * 60)
