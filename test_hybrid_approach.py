#!/usr/bin/env python3
"""
Test hybrid approach for <PERSON> case
"""

def test_hybrid_approach():
    """Test the hybrid approach that handles contradictions"""
    
    test_cases = [
        {
            "name": "Tommy <PERSON> Contradiction Case",
            "set_score": "1-0",
            "winner_name": "<PERSON>",
            "player1": "SIN",
            "player2": "PAU", 
            "expected": "PAU",
            "reason": "Winner name '<PERSON>' contains PAU → Trust name over set score"
        },
        {
            "name": "Khachanov No Contradiction",
            "set_score": "1-0",
            "winner_name": "<PERSON>",
            "player1": "KHA",
            "player2": "ALC",
            "expected": "KHA",
            "reason": "Set score and winner name both indicate KHA"
        },
        {
            "name": "Alcaraz Player 2 wins",
            "set_score": "0-1", 
            "winner_name": "<PERSON>",
            "player1": "KHA",
            "player2": "ALC",
            "expected": "ALC",
            "reason": "Set score 0-1 and winner name both indicate ALC"
        },
        {
            "name": "No winner name",
            "set_score": "1-0",
            "winner_name": "",
            "player1": "DJ<PERSON>",
            "player2": "NAD",
            "expected": "<PERSON><PERSON>",
            "reason": "No winner name, trust set score 1-0 → DJO"
        }
    ]
    
    print("🧪 Hybrid Approach Test (Score + Name Validation)")
    print("=" * 60)
    print("Handles contradictions between set score and winner name")
    print()
    
    all_pass = True
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"📋 TEST {i}: {test_case['name']}")
        print(f"Set Score: {test_case['set_score']}")
        print(f"Winner Name: '{test_case['winner_name']}'")
        print(f"Player Codes: {test_case['player1']} (P1), {test_case['player2']} (P2)")
        print(f"Expected: {test_case['expected']} - {test_case['reason']}")
        
        result = simulate_hybrid_logic(
            test_case['set_score'],
            test_case['winner_name'],
            test_case['player1'],
            test_case['player2']
        )
        
        success = result['winner'] == test_case['expected']
        status = "✅ PASS" if success else "❌ FAIL"
        
        print(f"Result: {status} - {result['winner']} ({result['logic']})")
        
        if not success:
            all_pass = False
            print(f"   Expected: {test_case['expected']}, Got: {result['winner']}")
        
        print()
    
    print("=" * 60)
    print(f"🏆 OVERALL RESULT: {'✅ ALL TESTS PASS' if all_pass else '❌ SOME TESTS FAILED'}")
    
    if all_pass:
        print("\n🎾 Hybrid approach is working perfectly!")
        print("   Handles contradictions and validates against player codes! 🚀")

def simulate_hybrid_logic(set_score: str, winner_name: str, player1_code: str, player2_code: str) -> dict:
    """Simulate the hybrid logic"""
    try:
        # Parse set score
        score_parts = set_score.split('-')
        score1 = int(score_parts[0])
        score2 = int(score_parts[1])
        
        print(f"  🔍 Set score: {score1}-{score2}")
        print(f"  🔍 Winner name: '{winner_name}'")
        print(f"  🔍 Player codes: {player1_code} (P1), {player2_code} (P2)")
        
        # Step 1: Determine winner from set score
        if score1 == 1 and score2 == 0:
            set_score_winner = player1_code
            print(f"  🔍 Set score 1-0 → Player 1 ({player1_code}) won first game")
        elif score1 == 0 and score2 == 1:
            set_score_winner = player2_code
            print(f"  🔍 Set score 0-1 → Player 2 ({player2_code}) won first game")
        else:
            set_score_winner = player1_code
            print(f"  🔍 Fallback → Player 1 ({player1_code})")
        
        # Step 2: Validate against winner name if available
        if winner_name:
            # Check if winner name contains either player code
            name_indicates_player1 = player1_code in winner_name
            name_indicates_player2 = player2_code in winner_name
            
            if name_indicates_player1 and not name_indicates_player2:
                name_winner = player1_code
                print(f"  🔍 Winner name contains '{player1_code}' → Player 1")
            elif name_indicates_player2 and not name_indicates_player1:
                name_winner = player2_code
                print(f"  🔍 Winner name contains '{player2_code}' → Player 2")
            else:
                name_winner = None
                print(f"  🔍 Winner name doesn't clearly indicate player code")
            
            # Step 3: Handle contradictions
            if name_winner and name_winner != set_score_winner:
                print(f"  🔍 CONTRADICTION: Set score says {set_score_winner}, name says {name_winner}")
                print(f"  🔍 Trusting winner name: {name_winner}")
                winner_code = name_winner
                logic = f"Contradiction resolved: trusted name '{winner_name}' over set score"
            else:
                print(f"  🔍 No contradiction, using set score winner: {set_score_winner}")
                winner_code = set_score_winner
                logic = f"Set score and name agree: {set_score_winner}"
        else:
            print(f"  🔍 No winner name, using set score winner: {set_score_winner}")
            winner_code = set_score_winner
            logic = f"No name available, used set score: {set_score_winner}"
        
        return {
            "winner": winner_code,
            "logic": logic
        }
        
    except Exception as e:
        return {
            "winner": player1_code,
            "logic": f"Error: {e}"
        }

if __name__ == "__main__":
    test_hybrid_approach()
