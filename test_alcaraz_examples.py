#!/usr/bin/env python3
"""
Test script to verify the improved starting server detection logic works with Alcaraz examples.
"""

def extract_player_codes_from_data_test(match_data_text):
    """Test version of extract_player_codes_from_data method"""
    try:
        if not match_data_text:
            return None

        lines = match_data_text.split('\n')
        found_codes = []

        # Look for player codes in various formats
        for line in lines:
            line = line.strip()

            # Skip empty lines
            if not line:
                continue

            extracted_code = None

            # Pattern 1: Prefixed codes like "I.MON", "J.MON" (single letter + period + code)
            if len(line) >= 3 and '.' in line:
                parts = line.split('.')
                if len(parts) == 2 and len(parts[0]) == 1 and parts[0].isalpha():
                    # Extract the code part after the period
                    code_part = parts[1].strip()
                    if len(code_part) >= 2 and code_part.isalpha() and code_part.isupper():
                        # Keep the full prefixed code (e.g., "I.MON" instead of just "MON")
                        extracted_code = line

            # Pattern 2: Plain codes (2+ letters, all uppercase)
            elif len(line) >= 2 and line.isalpha() and line.isupper():
                extracted_code = line

            # Validate and add the extracted code
            if extracted_code:
                # Avoid common non-player codes and tennis terms
                excluded_codes = {'SET', 'WIN', 'END', 'TIE', 'ADV', 'ACE', 'UNF', 'NET', 'OUT', 'DBF',
                                'TIED', 'GAME', 'MATCH', 'POINT', 'SERVE', 'RETURN', 'BREAK', 'HOLD'}

                if extracted_code not in excluded_codes:
                    found_codes.append(extracted_code)
                    # Stop after finding 2 codes (allow duplicates for same player codes)
                    if len(found_codes) >= 2:
                        break

        # Return the first two codes found (Player 1, Player 2)
        if len(found_codes) >= 2:
            return found_codes[:2]
        elif len(found_codes) == 1:
            return [found_codes[0], None]
        else:
            return None

    except Exception as e:
        print(f"Error extracting player codes from data: {e}")
        return None


def detect_starting_server_from_data_test(match_data_text):
    """Test version of the improved detect_starting_server_from_data method"""
    try:
        if not match_data_text:
            return None

        lines = match_data_text.split('\n')
        
        # Extract player codes first
        player_codes = extract_player_codes_from_data_test(match_data_text)
        if not player_codes or len(player_codes) < 2:
            return None
        
        player1_code, player2_code = player_codes[0], player_codes[1]
        print(f"Detected player codes: {player1_code}, {player2_code}")
        
        # Strategy: Find who won the first game (1-0 or 0-1)
        # The winner of the first game was serving
        i = 0
        while i < len(lines):
            line = lines[i].strip()
            
            # Look for first game result: 1-0 or 0-1
            if ((line == '1' and i + 2 < len(lines) and 
                 lines[i + 1].strip() == '-' and 
                 lines[i + 2].strip() == '0') or
                (line == '0' and i + 2 < len(lines) and 
                 lines[i + 1].strip() == '-' and 
                 lines[i + 2].strip() == '1')):
                
                score = f"{line}-{lines[i + 2].strip()}"
                print(f"Found first game result at line {i}: {score}")
                
                # Skip to after the score
                i += 3
                
                # Look for the winner's name
                while i < len(lines):
                    next_line = lines[i].strip()
                    print(f"  Checking line {i}: '{next_line}'")
                    
                    # Check if it's a player name (full name, not code)
                    if (any(char.isalpha() for char in next_line) and 
                        ' ' in next_line and 
                        next_line not in [player1_code, player2_code]):
                        
                        # This is the winner's name
                        winner_name = next_line.lower()
                        print(f"  Found winner name: '{next_line}'")
                        
                        # Match the winner name to player codes
                        # Check if any part of the player codes appears in the winner name
                        if any(code.lower() in winner_name for code in [player1_code.lower()]):
                            print(f"  Matched {player1_code} by code in name")
                            return player1_code
                        elif any(code.lower() in winner_name for code in [player2_code.lower()]):
                            print(f"  Matched {player2_code} by code in name")
                            return player2_code
                        
                        # If we can't match by code, try common name patterns
                        # For names like "Carlos Alcaraz" -> ALC, "Karen Khachanov" -> KHA
                        name_parts = winner_name.split()
                        if len(name_parts) >= 2:
                            first_name = name_parts[0]
                            last_name = name_parts[-1]
                            print(f"  Name parts: first='{first_name}', last='{last_name}'")
                            
                            # Check if player codes match name patterns
                            for code in [player1_code, player2_code]:
                                code_lower = code.lower()
                                print(f"    Checking code '{code}' against names")
                                # Check if code matches first letters of names
                                if (len(code_lower) >= 3 and 
                                    (code_lower.startswith(first_name[:2]) or 
                                     code_lower.startswith(last_name[:3]) or
                                     first_name.startswith(code_lower[:2]) or
                                     last_name.startswith(code_lower[:3]))):
                                    print(f"    Matched {code} by name pattern")
                                    return code
                        
                        # Fallback: if we can't determine from name, use score position
                        if line == '1' and lines[i-2].strip() == '0':  # 1-0 score
                            print(f"  Fallback: 1-0 score, returning {player1_code}")
                            return player1_code  # First player won
                        elif line == '0' and lines[i-2].strip() == '1':  # 0-1 score
                            print(f"  Fallback: 0-1 score, returning {player2_code}")
                            return player2_code  # Second player won
                        
                        break
                    
                    i += 1
                break
            
            i += 1
        
        print("  No first game result found, using fallback method")
        return None
        
    except Exception as e:
        print(f"Error detecting starting server from data: {e}")
        return None


def test_alcaraz_example_1():
    """Test with the first Alcaraz example where he should be detected as starting server"""
    print("=== Testing Alcaraz Example 1: ALC should be detected as starting server ===")
    
    example_1 = """0
-
0
tied
SIN
ALC
15
0
30
0
30
15
30
30
30
40
40
40
40
AD
1
-
0
Carlos Alcaraz
SIN
ALC
0
15
0
30
0
BP
40
2
-
0
Carlos Alcaraz"""
    
    result = detect_starting_server_from_data_test(example_1)
    print(f"Result: {result}")
    print(f"Expected: ALC")
    print(f"✅ PASS" if result == "ALC" else f"❌ FAIL")
    print()


def test_alcaraz_example_2():
    """Test with the second example where Khachanov wins but Alcaraz should be detected"""
    print("=== Testing Alcaraz Example 2: KHA wins first game, so KHA should be starting server ===")
    
    example_2 = """0
-
0
tied
KHA
ALC
0
15
15
15
15
30
30
30
BP
40
30
1
-
0
Karen Khachanov
KHA
ALC
0
15
15
15
30
15
40
15
2
-
0
Karen Khachanov"""
    
    result = detect_starting_server_from_data_test(example_2)
    print(f"Result: {result}")
    print(f"Expected: KHA (since Karen Khachanov won the first game)")
    print(f"✅ PASS" if result == "KHA" else f"❌ FAIL")
    print()


if __name__ == "__main__":
    print("Testing Improved Starting Server Detection Logic with Alcaraz Examples")
    print("=" * 70)
    print()
    
    test_alcaraz_example_1()
    test_alcaraz_example_2()
    
    print("Testing completed!")
