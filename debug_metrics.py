#!/usr/bin/env python3
"""
Debug script to check the AI learning system metrics
"""

from adaptive_learning_system import AdaptiveLearningSystem
import json

def debug_learning_metrics():
    """Debug the learning system metrics"""
    print("🔍 DEBUGGING AI LEARNING SYSTEM METRICS")
    print("=" * 50)
    
    # Load the system
    system = AdaptiveLearningSystem()
    
    # Check current metrics
    print('\n=== CURRENT LEARNING METRICS ===')
    print(f'Total predictions: {system.learning_metrics.total_predictions}')
    print(f'Correct predictions: {system.learning_metrics.correct_predictions}')
    print(f'Accuracy: {system.learning_metrics.accuracy}')
    
    # Check weight configurations
    print('\n=== WEIGHT CONFIGURATION METRICS ===')
    print(f'Current weights accuracy_score: {system.current_weights.accuracy_score}')
    print(f'Current weights sample_size: {system.current_weights.sample_size}')
    print(f'Current weights confidence_interval: {system.current_weights.confidence_interval}')
    
    # Check if there are any completed predictions
    completed_preds = [p for p in system.prediction_tracker.predictions 
                      if p.actual_winner is not None and p.is_ai_prediction]
    print(f'\nCompleted AI predictions in tracker: {len(completed_preds)}')
    
    # Check the weight configurations file
    try:
        with open('learning_data/weight_configurations.json', 'r') as f:
            config_data = json.load(f)
            
        print('\n=== WEIGHT CONFIG FILE METRICS ===')
        current_data = config_data['current']
        print(f'Current accuracy_score: {current_data["accuracy_score"]}')
        print(f'Current sample_size: {current_data["sample_size"]}')
        
        clay_data = config_data['surface_weights']['clay']
        print(f'Clay accuracy_score: {clay_data["accuracy_score"]}')
        print(f'Clay sample_size: {clay_data["sample_size"]}')
        
        hard_data = config_data['surface_weights']['hard']
        print(f'Hard accuracy_score: {hard_data["accuracy_score"]}')
        print(f'Hard sample_size: {hard_data["sample_size"]}')
        
        grass_data = config_data['surface_weights']['grass']
        print(f'Grass accuracy_score: {grass_data["accuracy_score"]}')
        print(f'Grass sample_size: {grass_data["sample_size"]}')
        
    except Exception as e:
        print(f"Error reading weight config file: {e}")
    
    # Check enhanced learning system
    try:
        from enhanced_adaptive_learning_system import enhanced_learning_system
        
        print('\n=== ENHANCED LEARNING SYSTEM ===')
        contextual_preds = enhanced_learning_system.contextual_predictions
        print(f'Total contextual predictions: {len(contextual_preds)}')
        
        completed_contextual = [p for p in contextual_preds 
                               if p.actual_winner is not None]
        print(f'Completed contextual predictions: {len(completed_contextual)}')
        
        # Check balance file
        with open('enhanced_learning_data/historical_momentum_balance.json', 'r') as f:
            balance_data = json.load(f)
            
        print(f'Balance accuracy_score: {balance_data["accuracy_score"]}')
        print(f'Balance sample_size: {balance_data["sample_size"]}')
        
    except Exception as e:
        print(f"Error checking enhanced system: {e}")

def recalculate_all_system_metrics():
    """Recalculate metrics for both learning systems"""
    print("\n🔧 RECALCULATING ALL SYSTEM METRICS")
    print("=" * 50)

    # Recalculate base learning system metrics
    try:
        from adaptive_learning_system import AdaptiveLearningSystem

        print("\n1️⃣ Recalculating Base Learning System...")
        base_system = AdaptiveLearningSystem()
        base_system.recalculate_all_metrics()

    except Exception as e:
        print(f"❌ Error recalculating base system: {e}")

    # Recalculate enhanced learning system metrics
    try:
        from enhanced_adaptive_learning_system import enhanced_learning_system

        print("\n2️⃣ Recalculating Enhanced Learning System...")
        enhanced_learning_system.recalculate_all_metrics()

    except Exception as e:
        print(f"❌ Error recalculating enhanced system: {e}")

    print("\n✅ All metrics recalculation completed!")
    print("\nRe-running debug to verify fixes...")
    debug_learning_metrics()

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "--recalculate":
        recalculate_all_system_metrics()
    else:
        debug_learning_metrics()
