#!/usr/bin/env python3
"""
Debug momentum factor calculations to see why they're zeroed
"""

import json
from enhanced_gemini_integration import EnhancedGeminiAnalyzer

def debug_momentum_calculations():
    """Debug momentum factor calculations"""
    print("🔍 DEBUGGING MOMENTUM FACTOR CALCULATIONS")
    print("=" * 50)
    
    # Load a sample prediction from prediction_history.json to see the data structure
    try:
        with open('prediction_history.json', 'r') as f:
            predictions = json.load(f)
        
        if predictions:
            sample_pred = predictions[0]  # Get first prediction
            print("=== SAMPLE PREDICTION DATA STRUCTURE ===")
            print(f"Player codes: {sample_pred.get('player1_code')} vs {sample_pred.get('player2_code')}")
            
            # Check momentum_factors structure
            momentum_factors = sample_pred.get('momentum_factors', {})
            print(f"\nMomentum factors keys: {list(momentum_factors.keys())}")
            
            if momentum_factors:
                # Check first player's data
                first_player = list(momentum_factors.keys())[0]
                player_data = momentum_factors[first_player]
                print(f"\n=== {first_player} DATA STRUCTURE ===")
                print(f"Keys: {list(player_data.keys())}")
                
                # Check specific fields that should be used for calculations
                print(f"\nSpecific fields:")
                print(f"  momentum_intensity: {player_data.get('momentum_intensity', 'MISSING')}")
                print(f"  serving_rhythm: {player_data.get('serving_rhythm', 'MISSING')}")
                print(f"  pressure_metrics: {player_data.get('pressure_metrics', 'MISSING')}")
                print(f"  recent_three_point_runs: {player_data.get('recent_three_point_runs', 'MISSING')}")
                
                # Test the extraction methods
                print(f"\n=== TESTING EXTRACTION METHODS ===")
                analyzer = EnhancedGeminiAnalyzer()
                
                # Test momentum intensity extraction
                momentum_intensity = analyzer._get_momentum_intensity(player_data)
                print(f"Extracted momentum_intensity: {momentum_intensity}")
                
                # Test service consistency extraction
                service_consistency = analyzer._get_service_consistency(player_data)
                print(f"Extracted service_consistency: {service_consistency}")
                
                # Test mental fatigue extraction
                mental_fatigue = analyzer._get_mental_fatigue(player_data)
                print(f"Extracted mental_fatigue: {mental_fatigue}")
                
                # Test full momentum factor extraction
                print(f"\n=== TESTING FULL MOMENTUM EXTRACTION ===")
                player1_code = sample_pred.get('player1_code')
                player2_code = sample_pred.get('player2_code')
                
                extracted_factors = analyzer._extract_momentum_factors(
                    momentum_factors, player1_code, player2_code
                )
                print(f"Extracted momentum factors: {extracted_factors}")
                
    except Exception as e:
        print(f"Error debugging momentum calculations: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_momentum_calculations()
