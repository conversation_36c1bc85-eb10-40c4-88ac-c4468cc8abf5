#!/usr/bin/env python3
"""
Test Tournament Level Statistics Implementation
Tests the new tournament level statistics functionality in the AI learning system.
"""

import sys
import os
from datetime import datetime
from typing import Dict, Any

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from prediction_tracker import PredictionTracker, PredictionRecord

def create_test_predictions() -> PredictionTracker:
    """Create a tracker with test predictions for different tournament levels"""
    tracker = PredictionTracker()
    
    # Test data for different tournament levels
    test_predictions = [
        # ATP predictions
        {
            'tournament_level': 'ATP',
            'surface': 'Hard',
            'set_number': 1,
            'score': (3, 3),
            'predicted_winner': 'NOV',
            'actual_winner': 'NOV',
            'is_ai_prediction': True,
            'confidence': 0.75
        },
        {
            'tournament_level': 'ATP',
            'surface': 'Clay',
            'set_number': 2,
            'score': (4, 4),
            'predicted_winner': 'NAD',
            'actual_winner': 'FED',
            'is_ai_prediction': True,
            'confidence': 0.65
        },
        {
            'tournament_level': 'ATP',
            'surface': 'Hard',
            'set_number': 1,
            'score': (5, 5),
            'predicted_winner': 'DJO',
            'actual_winner': 'DJO',
            'is_ai_prediction': False,
            'confidence': 0.55
        },
        
        # Challenger predictions
        {
            'tournament_level': 'Challenger',
            'surface': 'Hard',
            'set_number': 1,
            'score': (3, 3),
            'predicted_winner': 'SMI',
            'actual_winner': 'SMI',
            'is_ai_prediction': True,
            'confidence': 0.80
        },
        {
            'tournament_level': 'Challenger',
            'surface': 'Clay',
            'set_number': 2,
            'score': (4, 4),
            'predicted_winner': 'JON',
            'actual_winner': 'JON',
            'is_ai_prediction': False,
            'confidence': 0.60
        },
        
        # WTA predictions
        {
            'tournament_level': 'WTA',
            'surface': 'Hard',
            'set_number': 1,
            'score': (3, 3),
            'predicted_winner': 'SER',
            'actual_winner': 'SER',
            'is_ai_prediction': True,
            'confidence': 0.70
        },
        {
            'tournament_level': 'WTA',
            'surface': 'Grass',
            'set_number': 3,
            'score': (6, 6),
            'predicted_winner': 'WIL',
            'actual_winner': 'OSA',
            'is_ai_prediction': False,
            'confidence': 0.45
        },
        
        # Mixed predictions
        {
            'tournament_level': 'Mixed',
            'surface': 'Hard',
            'set_number': 1,
            'score': (3, 3),
            'predicted_winner': 'PLR1',
            'actual_winner': 'PLR1',
            'is_ai_prediction': True,
            'confidence': 0.85
        }
    ]
    
    # Add predictions to tracker
    for i, pred_data in enumerate(test_predictions):
        prediction = PredictionRecord(
            timestamp=datetime.now().isoformat(),
            score=pred_data['score'],
            player1_name=f"Player {i*2+1}",
            player2_name=f"Player {i*2+2}",
            player1_code=f"P{i*2+1}",
            player2_code=f"P{i*2+2}",
            predicted_winner=pred_data['predicted_winner'],
            prediction_probability=0.6,
            confidence=pred_data['confidence'],
            actual_winner=pred_data['actual_winner'],
            set_number=pred_data['set_number'],
            surface=pred_data['surface'],
            is_ai_prediction=pred_data['is_ai_prediction'],
            learning_metadata={
                'tournament_level': pred_data['tournament_level'],
                'timestamp': datetime.now().isoformat(),
                'gui_version': 'test_v1'
            }
        )
        
        tracker.predictions.append(prediction)
    
    return tracker

def test_tournament_level_statistics():
    """Test the tournament level statistics functionality"""
    print("🧪 Testing Tournament Level Statistics Implementation")
    print("=" * 60)
    
    # Create test tracker with predictions
    tracker = create_test_predictions()
    
    print(f"📊 Created {len(tracker.predictions)} test predictions")
    print()
    
    # Test tournament level statistics
    print("🏆 Tournament Level Statistics:")
    print("-" * 40)
    
    tournament_stats = tracker.get_statistics_by_tournament_level()
    
    for tournament_level, stats in tournament_stats.items():
        print(f"\n{tournament_level}:")
        print(f"  Total Predictions: {stats['total']}")
        print(f"  Correct: {stats['correct']}")
        print(f"  Accuracy: {stats['accuracy']:.1f}%")
        print(f"  AI Predictions: {stats['ai_predictions']['total']} ({stats['ai_predictions']['accuracy']:.1f}%)")
        print(f"  Math Predictions: {stats['math_predictions']['total']} ({stats['math_predictions']['accuracy']:.1f}%)")
        print(f"  Average Confidence: {stats['avg_confidence']:.1f}%")
        
        # Surface breakdown
        if stats['surface_breakdown']:
            print(f"  Surface Breakdown:")
            for surface, surface_stats in stats['surface_breakdown'].items():
                print(f"    {surface}: {surface_stats['accuracy']:.1f}% ({surface_stats['total']} predictions)")
        
        # Set breakdown
        if stats['set_breakdown']:
            print(f"  Set Breakdown:")
            for set_num, set_stats in stats['set_breakdown'].items():
                print(f"    Set {set_num}: {set_stats['accuracy']:.1f}% ({set_stats['total']} predictions)")
    
    print("\n" + "=" * 60)
    print("✅ Tournament Level Statistics Test Complete!")
    
    return tournament_stats

def test_export_with_tournament_stats():
    """Test exporting statistics with tournament level data"""
    print("\n📤 Testing Export with Tournament Level Statistics")
    print("-" * 50)
    
    tracker = create_test_predictions()
    
    # Export to test file
    test_filename = "test_tournament_statistics_export.txt"
    tracker.export_statistics(test_filename)
    
    print(f"✅ Exported statistics to {test_filename}")
    
    # Check if file contains tournament level section
    try:
        with open(test_filename, 'r') as f:
            content = f.read()
            if "TOURNAMENT LEVEL STATISTICS" in content:
                print("✅ Tournament level statistics found in export")
                
                # Count tournament level entries
                lines = content.split('\n')
                tournament_section = False
                tournament_count = 0
                
                for line in lines:
                    if "TOURNAMENT LEVEL STATISTICS" in line:
                        tournament_section = True
                    elif tournament_section and line.startswith("PREDICTION PATTERNS"):
                        break
                    elif tournament_section and line.strip() and not line.startswith('-') and not line.startswith('Tournament'):
                        if any(level in line for level in ['ATP', 'Challenger', 'WTA', 'Mixed']):
                            tournament_count += 1
                
                print(f"✅ Found {tournament_count} tournament level entries in export")
            else:
                print("❌ Tournament level statistics not found in export")
                
    except Exception as e:
        print(f"❌ Error reading export file: {e}")
    
    # Clean up test file
    try:
        os.remove(test_filename)
        print(f"🧹 Cleaned up test file: {test_filename}")
    except:
        pass

if __name__ == "__main__":
    try:
        # Run tests
        tournament_stats = test_tournament_level_statistics()
        test_export_with_tournament_stats()
        
        print("\n🎉 All Tournament Level Statistics Tests Passed!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
