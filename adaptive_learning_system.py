"""
Adaptive Learning System for Tennis Prediction AI
Continuously improves prediction accuracy by learning from historical results
"""

import json
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict, field
from pathlib import Path
import sqlite3
from scipy import stats
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error
import warnings
warnings.filterwarnings('ignore')

from prediction_tracker import PredictionTracker, PredictionRecord


@dataclass
class WeightConfiguration:
    """Configuration for prompt weights"""
    service_consistency_weight: float = 0.25
    mental_fatigue_weight: float = 0.15
    service_pressure_weight: float = 0.15
    momentum_intensity_weight: float = 0.20
    clutch_performance_weight: float = 0.05
    current_hold_streak_weight: float = 0.10
    deuce_game_performance_weight: float = 0.10
    
    # Context multipliers
    context_multiplier: float = 1.0
    break_point_pressure: float = 1.0
    
    # Metadata
    version: str = "1.0"
    created_at: str = ""
    accuracy_score: float = 0.0
    sample_size: int = 0
    confidence_interval: Tuple[float, float] = (0.0, 0.0)
    
    def __post_init__(self):
        if not self.created_at:
            self.created_at = datetime.now().isoformat()
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'WeightConfiguration':
        return cls(**data)


@dataclass
class SurfaceSpecificWeights:
    """Surface-specific weight configurations"""
    clay_weights: 'WeightConfiguration' = None
    hard_weights: 'WeightConfiguration' = None
    grass_weights: 'WeightConfiguration' = None

    def __post_init__(self):
        if self.clay_weights is None:
            self.clay_weights = WeightConfiguration()
        if self.hard_weights is None:
            self.hard_weights = WeightConfiguration()
        if self.grass_weights is None:
            self.grass_weights = WeightConfiguration()

    def get_weights_for_surface(self, surface: str) -> 'WeightConfiguration':
        surface_map = {
            'Clay': self.clay_weights,
            'Hard': self.hard_weights,
            'Grass': self.grass_weights
        }
        return surface_map.get(surface, self.hard_weights)


@dataclass
class SurfaceMomentumWeights:
    """Surface-specific momentum weight configurations"""
    clay_momentum: Dict[str, float] = field(default_factory=lambda: {
        'momentum_intensity_weight': 0.18,  # Lower - longer rallies, less momentum swings
        'mental_fatigue_weight': 0.20,      # Higher - longer matches
        'service_pressure_weight': 0.12,    # Lower - less service dominance
        'clutch_performance_weight': 0.08   # Higher - mental game important
    })

    hard_momentum: Dict[str, float] = field(default_factory=lambda: {
        'momentum_intensity_weight': 0.22,  # Balanced
        'mental_fatigue_weight': 0.15,      # Standard
        'service_pressure_weight': 0.15,    # Balanced
        'clutch_performance_weight': 0.05   # Standard
    })

    grass_momentum: Dict[str, float] = field(default_factory=lambda: {
        'momentum_intensity_weight': 0.25,  # Higher - quick momentum shifts
        'mental_fatigue_weight': 0.12,      # Lower - shorter points
        'service_pressure_weight': 0.18,    # Higher - service dominance
        'clutch_performance_weight': 0.06   # Slightly higher
    })


@dataclass
class EnhancedSampleRequirements:
    """Enhanced sample size requirements for different contexts - Research-based optimal sizes"""

    # Base requirements by weight type - Updated based on research findings
    weight_type_minimums: Dict[str, int] = field(default_factory=lambda: {
        'momentum_intensity_weight': 140,     # Up from 50 - highest volatility requires more data
        'mental_fatigue_weight': 120,         # Up from 30 - complex psychological measurement
        'service_pressure_weight': 110,       # Up from 25 - situational complexity
        'clutch_performance_weight': 150,     # Up from 40 - rare, high-impact situations
        'service_consistency_weight': 80,     # Up from 20 - most stable, frequent observations
        'current_hold_streak_weight': 90,     # Up from 25 - relatively stable pattern
        'deuce_game_performance_weight': 130  # New - high-variance situations
    })

    # Surface-specific multipliers - Research-validated optimal ratios
    surface_multipliers: Dict[str, float] = field(default_factory=lambda: {
        'Clay': 1.3,    # Research-based: 1.3x multiplier due to complexity
        'Hard': 1.0,    # Baseline (200 samples)
        'Grass': 1.5    # Research-based: 1.5x multiplier due to volatility
    })

    # Context-specific requirements - Updated based on research findings
    context_minimums: Dict[str, int] = field(default_factory=lambda: {
        'early_set': 70,      # Up from 15 - Games 0-4, simpler patterns
        'mid_set': 100,       # Up from 25 - Games 5-8, standard
        'late_set': 140,      # Up from 35 - Games 9+, critical moments
        'tiebreak': 160,      # Up from 20 - Specific tiebreak data needs more samples
        'break_point': 130    # Up from 30 - High-pressure situations
    })

    # Quality thresholds
    confidence_threshold: float = 0.7     # Minimum confidence in data quality
    diversity_threshold: float = 0.6      # Minimum diversity in scenarios


@dataclass
class LearningMetrics:
    """Metrics for tracking learning performance"""
    total_predictions: int = 0
    correct_predictions: int = 0
    accuracy: float = 0.0
    accuracy_trend: List[float] = None
    best_accuracy: float = 0.0
    worst_accuracy: float = 100.0
    improvement_rate: float = 0.0
    last_updated: str = ""

    def __post_init__(self):
        if self.accuracy_trend is None:
            self.accuracy_trend = []
        if not self.last_updated:
            self.last_updated = datetime.now().isoformat()


class AdaptiveLearningSystem:
    """Main adaptive learning system for tennis predictions"""
    
    def __init__(self, storage_dir: str = "learning_data"):
        self.storage_dir = Path(storage_dir)
        self.storage_dir.mkdir(exist_ok=True)
        
        # Initialize components
        self.prediction_tracker = PredictionTracker()
        self.weights_file = self.storage_dir / "weight_configurations.json"
        self.metrics_file = self.storage_dir / "learning_metrics.json"
        self.db_path = self.storage_dir / "learning_database.db"
        
        # Current configuration
        self.current_weights = WeightConfiguration()
        self.learning_metrics = LearningMetrics()

        # Enhanced configurations
        self.surface_weights = SurfaceSpecificWeights()
        self.momentum_weights = SurfaceMomentumWeights()
        self.sample_requirements = EnhancedSampleRequirements()

        # Learning parameters - Research-based optimal settings
        self.min_sample_size = 100  # Up from 20 - Research-based minimum for reliable learning
        self.significance_threshold = 0.01  # More conservative (1%)
        self.max_weight_change = 0.05  # Smaller weight changes
        self.learning_rate = 0.03  # Slower adaptation

        # Surface-specific sensitivity settings
        self.surface_sensitivity = {
            'Clay': 0.20,    # Most conservative - patterns are stable
            'Hard': 0.25,    # Moderate - balanced surface
            'Grass': 0.30    # Slightly higher - more volatile
        }

        # Weight type sensitivity settings
        self.weight_type_sensitivity = {
            'momentum_intensity_weight': 0.20,     # Very conservative
            'mental_fatigue_weight': 0.25,        # Moderate
            'service_pressure_weight': 0.30,      # Can adjust more
            'clutch_performance_weight': 0.15     # Very stable trait
        }

        # Weight bounds for momentum factors
        self.momentum_weight_bounds = {
            'momentum_intensity_weight': (0.15, 0.25),
            'mental_fatigue_weight': (0.10, 0.25),
            'service_pressure_weight': (0.10, 0.20),
            'clutch_performance_weight': (0.03, 0.10)
        }
        
        # Load existing data
        self.load_configurations()
        self.load_metrics()
        # Note: Database initialization removed - using JSON-only storage for consistency
    
    def init_database(self):
        """Initialize SQLite database for learning data"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS weight_performance (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    weight_config TEXT NOT NULL,
                    accuracy REAL NOT NULL,
                    sample_size INTEGER NOT NULL,
                    context_type TEXT,
                    surface TEXT,
                    set_number INTEGER,
                    score_type TEXT
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS learning_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_start TEXT NOT NULL,
                    session_end TEXT,
                    initial_accuracy REAL,
                    final_accuracy REAL,
                    improvement REAL,
                    predictions_analyzed INTEGER,
                    weight_changes INTEGER
                )
            """)
    
    def load_configurations(self):
        """Load weight configurations from file including surface-specific weights"""
        if self.weights_file.exists():
            try:
                with open(self.weights_file, 'r') as f:
                    data = json.load(f)
                    if 'current' in data:
                        self.current_weights = WeightConfiguration.from_dict(data['current'])

                    # Load surface-specific weights
                    if 'surface_weights' in data:
                        surface_data = data['surface_weights']
                        if 'clay' in surface_data:
                            self.surface_weights.clay_weights = WeightConfiguration.from_dict(surface_data['clay'])
                        if 'hard' in surface_data:
                            self.surface_weights.hard_weights = WeightConfiguration.from_dict(surface_data['hard'])
                        if 'grass' in surface_data:
                            self.surface_weights.grass_weights = WeightConfiguration.from_dict(surface_data['grass'])

                    # Load momentum weights
                    if 'momentum_weights' in data:
                        momentum_data = data['momentum_weights']
                        if 'clay_momentum' in momentum_data:
                            self.momentum_weights.clay_momentum = momentum_data['clay_momentum']
                        if 'hard_momentum' in momentum_data:
                            self.momentum_weights.hard_momentum = momentum_data['hard_momentum']
                        if 'grass_momentum' in momentum_data:
                            self.momentum_weights.grass_momentum = momentum_data['grass_momentum']

                    # Load sample requirements
                    if 'sample_requirements' in data:
                        req_data = data['sample_requirements']
                        if 'weight_type_minimums' in req_data:
                            self.sample_requirements.weight_type_minimums = req_data['weight_type_minimums']
                        if 'surface_multipliers' in req_data:
                            self.sample_requirements.surface_multipliers = req_data['surface_multipliers']
                        if 'context_minimums' in req_data:
                            self.sample_requirements.context_minimums = req_data['context_minimums']
                        if 'confidence_threshold' in req_data:
                            self.sample_requirements.confidence_threshold = req_data['confidence_threshold']
                        if 'diversity_threshold' in req_data:
                            self.sample_requirements.diversity_threshold = req_data['diversity_threshold']

            except Exception as e:
                print(f"Error loading weight configurations: {e}")
    
    def save_configurations(self):
        """Save weight configurations to file including surface-specific weights"""
        try:
            # Update accuracy metrics before saving
            self._update_weight_configuration_metrics()

            data = {
                'current': self.current_weights.to_dict(),
                'surface_weights': {
                    'clay': self.surface_weights.clay_weights.to_dict(),
                    'hard': self.surface_weights.hard_weights.to_dict(),
                    'grass': self.surface_weights.grass_weights.to_dict()
                },
                'momentum_weights': {
                    'clay_momentum': self.momentum_weights.clay_momentum,
                    'hard_momentum': self.momentum_weights.hard_momentum,
                    'grass_momentum': self.momentum_weights.grass_momentum
                },
                'sample_requirements': {
                    'weight_type_minimums': self.sample_requirements.weight_type_minimums,
                    'surface_multipliers': self.sample_requirements.surface_multipliers,
                    'context_minimums': self.sample_requirements.context_minimums,
                    'confidence_threshold': self.sample_requirements.confidence_threshold,
                    'diversity_threshold': self.sample_requirements.diversity_threshold
                },
                'last_updated': datetime.now().isoformat()
            }
            with open(self.weights_file, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            print(f"Error saving weight configurations: {e}")

    def _update_weight_configuration_metrics(self):
        """Update accuracy metrics for all weight configurations"""
        try:
            # Get completed AI predictions
            completed_predictions = [p for p in self.prediction_tracker.predictions
                                   if (p.actual_winner is not None and
                                       p.is_ai_prediction and
                                       self._is_prediction_eligible_for_learning(p))]

            # Update current weights metrics
            self._calculate_weight_metrics(self.current_weights, completed_predictions)

            # Update surface-specific weights metrics
            for surface in ['clay', 'hard', 'grass']:
                surface_predictions = [p for p in completed_predictions
                                     if p.surface and p.surface.lower() == surface]
                surface_weights = self.surface_weights.get_weights_for_surface(surface.title())
                self._calculate_weight_metrics(surface_weights, surface_predictions)

        except Exception as e:
            print(f"Error updating weight configuration metrics: {e}")

    def _calculate_weight_metrics(self, weight_config: WeightConfiguration,
                                predictions: List[PredictionRecord]):
        """Calculate accuracy metrics for a specific weight configuration"""
        if not predictions:
            weight_config.accuracy_score = 0.0
            weight_config.sample_size = 0
            weight_config.confidence_interval = (0.0, 0.0)
            return

        # Calculate accuracy
        correct_predictions = sum(1 for p in predictions
                                if p.predicted_winner == p.actual_winner)
        accuracy = correct_predictions / len(predictions)
        sample_size = len(predictions)

        # Calculate confidence interval (95% confidence)
        if sample_size >= 5:
            # Use normal approximation for binomial proportion
            z_score = 1.96  # 95% confidence
            std_error = np.sqrt(accuracy * (1 - accuracy) / sample_size)
            margin_error = z_score * std_error

            lower = max(0.0, accuracy - margin_error)
            upper = min(1.0, accuracy + margin_error)
            confidence_interval = (lower, upper)
        else:
            # Wide interval for small samples
            confidence_interval = (0.0, 1.0)

        # Update weight configuration
        weight_config.accuracy_score = accuracy
        weight_config.sample_size = sample_size
        weight_config.confidence_interval = confidence_interval

    def load_metrics(self):
        """Load learning metrics from file"""
        if self.metrics_file.exists():
            try:
                with open(self.metrics_file, 'r') as f:
                    data = json.load(f)
                    self.learning_metrics = LearningMetrics(**data)
            except Exception as e:
                print(f"Error loading learning metrics: {e}")
    
    def save_metrics(self):
        """Save learning metrics to file"""
        try:
            with open(self.metrics_file, 'w') as f:
                json.dump(asdict(self.learning_metrics), f, indent=2)
        except Exception as e:
            print(f"Error saving learning metrics: {e}")
    
    def get_current_weights(self, context: Dict[str, Any] = None) -> WeightConfiguration:
        """Get current weights, potentially adjusted for context"""
        if context is None:
            return self.current_weights

        # Get surface-specific weights if surface is provided
        surface = context.get('surface')
        if surface:
            return self.surface_weights.get_weights_for_surface(surface)

        return self.current_weights

    def apply_weight_bounds(self, weight_name: str, new_value: float) -> float:
        """Apply bounds to prevent extreme weight values"""
        if weight_name in self.momentum_weight_bounds:
            min_val, max_val = self.momentum_weight_bounds[weight_name]
            return max(min_val, min(max_val, new_value))
        return new_value

    def calculate_required_sample_size(self, weight_type: str, surface: str,
                                     context: str = 'standard') -> int:
        """Calculate dynamic sample size requirements"""

        # Base requirement for weight type - Updated fallback to research-based minimum
        base_requirement = self.sample_requirements.weight_type_minimums.get(weight_type, 100)

        # Apply surface multiplier
        surface_mult = self.sample_requirements.surface_multipliers.get(surface, 1.0)
        surface_adjusted = int(base_requirement * surface_mult)

        # Apply context requirement - Updated fallback to research-based minimum
        context_min = self.sample_requirements.context_minimums.get(context, 100)

        # Take the maximum of surface-adjusted and context minimum
        final_requirement = max(surface_adjusted, context_min)

        # Add quality-based adjustments
        if self._get_data_quality_score(weight_type, surface) < 0.7:
            final_requirement = int(final_requirement * 1.3)  # Need 30% more for low quality

        return final_requirement

    def _get_data_quality_score(self, weight_type: str, surface: str) -> float:
        """Assess quality of available data for specific weight/surface combination"""
        relevant_predictions = [p for p in self.prediction_tracker.predictions
                              if p.surface == surface and p.actual_winner is not None]

        if len(relevant_predictions) < 10:
            return 0.0

        # Check diversity of scenarios
        unique_scores = len(set((p.score[0], p.score[1]) for p in relevant_predictions))
        unique_players = len(set(p.predicted_winner for p in relevant_predictions))

        diversity_score = min(1.0, (unique_scores / 10) * (unique_players / 20))

        # Check recency distribution
        recent_count = len([p for p in relevant_predictions[-50:]])
        recency_score = min(1.0, recent_count / 25)

        return (diversity_score + recency_score) / 2

    def _assess_momentum_pattern_stability(self, weight_type: str, surface: str) -> float:
        """Assess if momentum patterns are stable enough for optimization"""
        recent_predictions = [p for p in self.prediction_tracker.predictions[-100:]
                             if p.surface == surface and p.actual_winner is not None]

        if len(recent_predictions) < 20:
            return 0.0

        # Check for consistent momentum impact patterns
        momentum_impacts = []
        for pred in recent_predictions:
            if pred.prompt_weights and weight_type in pred.prompt_weights:
                weight_value = pred.prompt_weights[weight_type]
                was_correct = pred.predicted_winner == pred.actual_winner
                momentum_impacts.append((weight_value, was_correct))

        if len(momentum_impacts) < 10:
            return 0.0

        # Calculate pattern consistency
        # Higher weights should correlate with better accuracy
        weights_array = np.array([w for w, _ in momentum_impacts])
        median_weight = np.median(weights_array)

        high_weight_accuracy = np.mean([correct for weight, correct in momentum_impacts
                                       if weight > median_weight])
        low_weight_accuracy = np.mean([correct for weight, correct in momentum_impacts
                                      if weight <= median_weight])

        # Stability = how consistent the weight-accuracy relationship is
        stability = abs(high_weight_accuracy - low_weight_accuracy)
        return min(1.0, stability * 2)  # Scale to 0-1

    def should_optimize_momentum_weights(self, surface: str) -> Dict[str, Any]:
        """Determine if momentum weights should be optimized for a surface"""
        momentum_weights = ['momentum_intensity_weight', 'mental_fatigue_weight',
                           'service_pressure_weight', 'clutch_performance_weight']

        optimization_status = {}

        for weight_type in momentum_weights:
            required_samples = self.calculate_required_sample_size(weight_type, surface)
            available_samples = self._count_relevant_samples(weight_type, surface)

            # Additional momentum-specific checks
            momentum_stability = self._assess_momentum_pattern_stability(weight_type, surface)
            recent_performance = self._get_recent_weight_performance(weight_type, surface)

            can_optimize = (
                available_samples >= required_samples and
                momentum_stability > 0.6 and  # Patterns are stable enough
                recent_performance.get('confidence', 0) > 0.5
            )

            optimization_status[weight_type] = {
                'can_optimize': can_optimize,
                'required_samples': required_samples,
                'available_samples': available_samples,
                'stability_score': momentum_stability,
                'missing_samples': max(0, required_samples - available_samples)
            }

        return optimization_status

    def _count_relevant_samples(self, weight_type: str, surface: str) -> int:
        """Count relevant samples for a specific weight type and surface"""
        relevant_predictions = [p for p in self.prediction_tracker.predictions
                              if (p.surface == surface and
                                  p.actual_winner is not None and
                                  p.prompt_weights and
                                  weight_type in p.prompt_weights)]
        return len(relevant_predictions)

    def _get_recent_weight_performance(self, weight_type: str, surface: str) -> Dict[str, float]:
        """Get recent performance metrics for a specific weight type and surface"""
        recent_predictions = [p for p in self.prediction_tracker.predictions[-50:]
                             if (p.surface == surface and
                                 p.actual_winner is not None and
                                 p.prompt_weights and
                                 weight_type in p.prompt_weights)]

        if len(recent_predictions) < 5:
            return {'confidence': 0.0, 'accuracy': 0.0}

        correct = sum(1 for p in recent_predictions if p.predicted_winner == p.actual_winner)
        accuracy = correct / len(recent_predictions)
        confidence = min(1.0, len(recent_predictions) / 20)  # Confidence based on sample size

        return {'confidence': confidence, 'accuracy': accuracy}

    def analyze_performance_over_time(self, window_size: int = 100) -> Dict[str, Any]:
        """Analyze accuracy trends over time windows"""
        completed = [p for p in self.prediction_tracker.predictions
                    if p.actual_winner is not None and p.is_ai_prediction]

        windows = []
        for i in range(0, len(completed), window_size):
            window = completed[i:i+window_size]
            if len(window) >= 10:  # Minimum window size
                correct = sum(1 for p in window if p.predicted_winner == p.actual_winner)
                accuracy = correct / len(window)
                windows.append({
                    'start_idx': i,
                    'end_idx': i + len(window),
                    'sample_size': len(window),
                    'accuracy': accuracy,
                    'timestamp_range': (window[0].timestamp, window[-1].timestamp)
                })

        return {'windows': windows, 'degradation_point': self._find_degradation_point(windows)}

    def _find_degradation_point(self, windows: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Find the point where performance started degrading"""
        if len(windows) < 3:
            return None

        # Find the best performing window
        best_window = max(windows, key=lambda w: w['accuracy'])
        best_idx = windows.index(best_window)

        # Look for significant drops after the best performance
        for i in range(best_idx + 1, len(windows)):
            current_accuracy = windows[i]['accuracy']
            if current_accuracy < best_window['accuracy'] - 0.05:  # 5% drop
                return {
                    'degradation_start': windows[i],
                    'peak_performance': best_window,
                    'accuracy_drop': best_window['accuracy'] - current_accuracy
                }

        return None
    
    def analyze_prediction_patterns(self) -> Dict[str, Any]:
        """Analyze patterns in prediction accuracy"""
        completed_predictions = [p for p in self.prediction_tracker.predictions 
                               if p.actual_winner is not None and p.is_ai_prediction]
        
        if len(completed_predictions) < self.min_sample_size:
            return {
                'status': 'insufficient_data',
                'sample_size': len(completed_predictions),
                'required_size': self.min_sample_size
            }
        
        # Calculate overall accuracy
        correct = sum(1 for p in completed_predictions 
                     if p.predicted_winner == p.actual_winner)
        accuracy = correct / len(completed_predictions)
        
        # Analyze by context
        patterns = {
            'overall_accuracy': accuracy,
            'sample_size': len(completed_predictions),
            'by_surface': self._analyze_by_surface(completed_predictions),
            'by_set_number': self._analyze_by_set_number(completed_predictions),
            'by_score_type': self._analyze_by_score_type(completed_predictions),
            'by_confidence': self._analyze_by_confidence(completed_predictions)
        }
        
        return patterns
    
    def _analyze_by_surface(self, predictions: List[PredictionRecord]) -> Dict[str, Dict[str, float]]:
        """Analyze accuracy by surface type"""
        surface_stats = {}
        for surface in ['Clay', 'Hard', 'Grass']:
            surface_preds = [p for p in predictions if p.surface == surface]
            if surface_preds:
                correct = sum(1 for p in surface_preds if p.predicted_winner == p.actual_winner)
                surface_stats[surface] = {
                    'accuracy': correct / len(surface_preds),
                    'sample_size': len(surface_preds)
                }
        return surface_stats
    
    def _analyze_by_set_number(self, predictions: List[PredictionRecord]) -> Dict[str, Dict[str, float]]:
        """Analyze accuracy by set number"""
        set_stats = {}
        for set_num in [1, 2, 3, 4, 5]:
            set_preds = [p for p in predictions if p.set_number == set_num]
            if set_preds:
                correct = sum(1 for p in set_preds if p.predicted_winner == p.actual_winner)
                set_stats[f'Set_{set_num}'] = {
                    'accuracy': correct / len(set_preds),
                    'sample_size': len(set_preds)
                }
        return set_stats
    
    def _analyze_by_score_type(self, predictions: List[PredictionRecord]) -> Dict[str, Dict[str, float]]:
        """Analyze accuracy by score type (tied, break point, etc.)"""
        score_stats = {}
        
        for pred in predictions:
            score = pred.score
            if score[0] == score[1]:  # Tied score
                score_type = f"Tied_{score[0]}_{score[1]}"
            elif abs(score[0] - score[1]) == 1:  # Break point scenario
                score_type = "Break_Point"
            else:
                score_type = "Other"
            
            if score_type not in score_stats:
                score_stats[score_type] = {'predictions': [], 'correct': 0}
            
            score_stats[score_type]['predictions'].append(pred)
            if pred.predicted_winner == pred.actual_winner:
                score_stats[score_type]['correct'] += 1
        
        # Calculate accuracies
        for score_type, data in score_stats.items():
            total = len(data['predictions'])
            score_stats[score_type] = {
                'accuracy': data['correct'] / total if total > 0 else 0,
                'sample_size': total
            }
        
        return score_stats
    
    def _analyze_by_confidence(self, predictions: List[PredictionRecord]) -> Dict[str, Dict[str, float]]:
        """Analyze accuracy by prediction confidence"""
        confidence_stats = {}
        
        # Group by confidence ranges
        ranges = [(0.5, 0.6), (0.6, 0.7), (0.7, 0.8), (0.8, 0.9), (0.9, 1.0)]
        
        for min_conf, max_conf in ranges:
            range_preds = [p for p in predictions 
                          if min_conf <= p.prediction_probability < max_conf]
            if range_preds:
                correct = sum(1 for p in range_preds if p.predicted_winner == p.actual_winner)
                confidence_stats[f'{min_conf:.1f}-{max_conf:.1f}'] = {
                    'accuracy': correct / len(range_preds),
                    'sample_size': len(range_preds)
                }
        
        return confidence_stats

    def optimize_weights(self, target_improvement: float = 0.02, surface: str = None) -> Dict[str, Any]:
        """Optimize weights based on historical performance with surface-specific logic"""
        patterns = self.analyze_prediction_patterns()

        if patterns.get('status') == 'insufficient_data':
            return patterns

        current_accuracy = patterns['overall_accuracy']

        # Get completed AI predictions with weight data
        completed_predictions = [p for p in self.prediction_tracker.predictions
                               if (p.actual_winner is not None and
                                   p.is_ai_prediction and
                                   p.prompt_weights is not None)]

        # Filter by surface if specified
        if surface:
            completed_predictions = [p for p in completed_predictions if p.surface == surface]

        if len(completed_predictions) < self.min_sample_size:
            return {
                'status': 'insufficient_weight_data',
                'sample_size': len(completed_predictions),
                'required_size': self.min_sample_size
            }

        # Check momentum weight optimization readiness if surface specified
        if surface:
            momentum_status = self.should_optimize_momentum_weights(surface)

            # Check if any momentum weights can't be optimized
            blocked_weights = [w for w, status in momentum_status.items()
                             if not status['can_optimize']]

            if blocked_weights:
                return {
                    'status': 'momentum_weights_not_ready',
                    'surface': surface,
                    'blocked_weights': blocked_weights,
                    'momentum_status': momentum_status
                }

        # Prepare data for optimization
        X, y = self._prepare_optimization_data(completed_predictions)

        if X.size == 0:
            return {'status': 'no_optimization_data'}

        # Perform weight optimization
        optimized_weights = self._gradient_descent_optimization(X, y, surface)

        # Validate improvements
        validation_result = self._validate_weight_improvements(
            completed_predictions, optimized_weights, current_accuracy
        )

        if validation_result['is_significant']:
            # Update weights
            old_weights = self.current_weights.to_dict()
            self._update_weights(optimized_weights, surface)

            # Log the change
            self._log_weight_change(old_weights, optimized_weights, validation_result)

            return {
                'status': 'weights_updated',
                'old_accuracy': current_accuracy,
                'predicted_accuracy': validation_result['predicted_accuracy'],
                'improvement': validation_result['improvement'],
                'confidence': validation_result['confidence'],
                'weight_changes': validation_result['weight_changes']
            }
        else:
            return {
                'status': 'no_significant_improvement',
                'current_accuracy': current_accuracy,
                'predicted_improvement': validation_result['improvement'],
                'confidence': validation_result['confidence']
            }

    def _prepare_optimization_data(self, predictions: List[PredictionRecord]) -> Tuple[np.ndarray, np.ndarray]:
        """Prepare data for weight optimization"""
        features = []
        targets = []

        for pred in predictions:
            if not pred.prompt_weights:
                continue

            # Extract weight features
            weights = pred.prompt_weights
            feature_vector = [
                weights.get('service_consistency_weight', 0.25),
                weights.get('mental_fatigue_weight', 0.15),
                weights.get('service_pressure_weight', 0.15),
                weights.get('momentum_intensity_weight', 0.20),
                weights.get('clutch_performance_weight', 0.05),
                weights.get('current_hold_streak_weight', 0.10),
                weights.get('deuce_game_performance_weight', 0.10)
            ]

            # Target: 1 if correct prediction, 0 if incorrect
            target = 1 if pred.predicted_winner == pred.actual_winner else 0

            features.append(feature_vector)
            targets.append(target)

        return np.array(features), np.array(targets)

    def _gradient_descent_optimization(self, X: np.ndarray, y: np.ndarray, surface: str = None) -> Dict[str, float]:
        """Perform gradient descent optimization on weights with enhanced bounds"""
        # Use logistic regression for binary classification
        from sklearn.linear_model import LogisticRegression

        try:
            model = LogisticRegression(random_state=42, max_iter=1000)
            model.fit(X, y)

            # Get optimized coefficients
            coefficients = model.coef_[0]

            # Convert to weight format (normalize to sum to 1.0)
            weights_sum = np.sum(np.abs(coefficients))
            if weights_sum > 0:
                normalized_weights = np.abs(coefficients) / weights_sum
            else:
                # Fallback to current weights
                current = self.current_weights
                normalized_weights = np.array([
                    current.service_consistency_weight,
                    current.mental_fatigue_weight,
                    current.service_pressure_weight,
                    current.momentum_intensity_weight,
                    current.clutch_performance_weight,
                    current.current_hold_streak_weight,
                    current.deuce_game_performance_weight
                ])

            # Apply learning rate and max change constraints
            current_weights_array = np.array([
                self.current_weights.service_consistency_weight,
                self.current_weights.mental_fatigue_weight,
                self.current_weights.service_pressure_weight,
                self.current_weights.momentum_intensity_weight,
                self.current_weights.clutch_performance_weight,
                self.current_weights.current_hold_streak_weight,
                self.current_weights.deuce_game_performance_weight
            ])

            # Get surface-specific learning rate if surface provided
            effective_learning_rate = self.learning_rate
            if surface and surface in self.surface_sensitivity:
                surface_factor = self.surface_sensitivity[surface] / 0.25  # Normalize to Hard court baseline
                effective_learning_rate *= surface_factor

            # Calculate weight changes with constraints
            weight_changes = (normalized_weights - current_weights_array) * effective_learning_rate
            weight_changes = np.clip(weight_changes, -self.max_weight_change, self.max_weight_change)

            new_weights = current_weights_array + weight_changes

            # Apply momentum-specific bounds
            weight_names = [
                'service_consistency_weight',
                'mental_fatigue_weight',
                'service_pressure_weight',
                'momentum_intensity_weight',
                'clutch_performance_weight',
                'current_hold_streak_weight',
                'deuce_game_performance_weight'
            ]

            for i, weight_name in enumerate(weight_names):
                new_weights[i] = self.apply_weight_bounds(weight_name, new_weights[i])

            # General bounds
            new_weights = np.clip(new_weights, 0.01, 0.6)  # Allow higher maximum weights

            # Normalize to sum to 1.0
            new_weights = new_weights / np.sum(new_weights)

            return {
                'service_consistency_weight': float(new_weights[0]),
                'mental_fatigue_weight': float(new_weights[1]),
                'service_pressure_weight': float(new_weights[2]),
                'momentum_intensity_weight': float(new_weights[3]),
                'clutch_performance_weight': float(new_weights[4]),
                'current_hold_streak_weight': float(new_weights[5]),
                'deuce_game_performance_weight': float(new_weights[6])
            }

        except Exception as e:
            print(f"Error in gradient descent optimization: {e}")
            # Return current weights as fallback
            return {
                'service_consistency_weight': self.current_weights.service_consistency_weight,
                'mental_fatigue_weight': self.current_weights.mental_fatigue_weight,
                'service_pressure_weight': self.current_weights.service_pressure_weight,
                'momentum_intensity_weight': self.current_weights.momentum_intensity_weight,
                'clutch_performance_weight': self.current_weights.clutch_performance_weight,
                'current_hold_streak_weight': self.current_weights.current_hold_streak_weight,
                'deuce_game_performance_weight': self.current_weights.deuce_game_performance_weight
            }

    def _validate_weight_improvements(self, predictions: List[PredictionRecord],
                                    new_weights: Dict[str, float],
                                    current_accuracy: float) -> Dict[str, Any]:
        """Validate that weight changes will improve accuracy"""
        # Simulate predictions with new weights
        simulated_accuracy = self._simulate_accuracy_with_weights(predictions, new_weights)

        improvement = simulated_accuracy - current_accuracy

        # Statistical significance test
        n = len(predictions)
        if n > 10:
            # Use simple statistical test for significance (scipy.stats.binom_test deprecated)
            try:
                from scipy.stats import binomtest
                correct_current = int(current_accuracy * n)
                correct_new = int(simulated_accuracy * n)

                result = binomtest(correct_new, n, current_accuracy)
                p_value = result.pvalue
                is_significant = p_value < self.significance_threshold
                confidence = 1 - p_value
            except ImportError:
                # Fallback to simple threshold-based test
                is_significant = improvement > 0.01  # Require at least 1% improvement
                confidence = min(0.9, improvement * 10)  # More generous heuristic confidence
        else:
            is_significant = improvement > 0.005  # Require at least 0.5% improvement for small samples
            confidence = max(0.3, min(0.8, improvement * 20))  # Better confidence for small samples

        return {
            'is_significant': is_significant,
            'improvement': improvement,
            'predicted_accuracy': simulated_accuracy,
            'confidence': confidence,
            'weight_changes': self._calculate_weight_changes(new_weights)
        }

    def _simulate_accuracy_with_weights(self, predictions: List[PredictionRecord],
                                      weights: Dict[str, float]) -> float:
        """Simulate accuracy with new weights"""
        # This is a simplified simulation
        # In practice, you'd re-run the prediction algorithm with new weights

        # For now, use a heuristic based on weight similarity to successful predictions
        correct_predictions = [p for p in predictions if p.predicted_winner == p.actual_winner]

        if not correct_predictions:
            return 0.5  # No data to base simulation on

        # Calculate average weights from successful predictions
        avg_successful_weights = {}
        weight_keys = ['service_consistency_weight', 'mental_fatigue_weight',
                      'service_pressure_weight', 'momentum_intensity_weight',
                      'clutch_performance_weight', 'current_hold_streak_weight',
                      'deuce_game_performance_weight']

        for key in weight_keys:
            values = [p.prompt_weights.get(key, 0.0) for p in correct_predictions
                     if p.prompt_weights and key in p.prompt_weights]
            avg_successful_weights[key] = np.mean(values) if values else weights[key]

        # Calculate similarity between new weights and successful weights
        similarity = self._calculate_weight_similarity(weights, avg_successful_weights)

        # Estimate accuracy based on similarity (heuristic)
        base_accuracy = len(correct_predictions) / len(predictions)
        estimated_accuracy = base_accuracy + (similarity - 0.5) * 0.1  # Max 10% improvement

        return max(0.0, min(1.0, estimated_accuracy))

    def _calculate_weight_similarity(self, weights1: Dict[str, float],
                                   weights2: Dict[str, float]) -> float:
        """Calculate similarity between two weight configurations"""
        keys = set(weights1.keys()) & set(weights2.keys())
        if not keys:
            return 0.5

        differences = [abs(weights1[key] - weights2[key]) for key in keys]
        avg_difference = np.mean(differences)

        # Convert difference to similarity (0 = completely different, 1 = identical)
        similarity = 1.0 - min(1.0, avg_difference * 2)  # Scale factor of 2
        return similarity

    def _calculate_weight_changes(self, new_weights: Dict[str, float]) -> Dict[str, float]:
        """Calculate changes from current weights"""
        current = self.current_weights
        changes = {}

        for key, new_value in new_weights.items():
            current_value = getattr(current, key, 0.0)
            changes[key] = new_value - current_value

        return changes

    def _update_weights(self, new_weights: Dict[str, float], surface: str = None):
        """Update current weight configuration with optional surface-specific updates"""
        # Update current weights
        for key, value in new_weights.items():
            if hasattr(self.current_weights, key):
                setattr(self.current_weights, key, value)

        # Update surface-specific weights if surface provided
        if surface:
            surface_weights = self.surface_weights.get_weights_for_surface(surface)
            for key, value in new_weights.items():
                if hasattr(surface_weights, key):
                    setattr(surface_weights, key, value)

        # Update metadata
        self.current_weights.version = f"{float(self.current_weights.version) + 0.1:.1f}"
        self.current_weights.created_at = datetime.now().isoformat()

        # Save updated configuration
        self.save_configurations()

        # Notify about weight update for GUI refresh
        surface_info = f" for {surface}" if surface else ""
        print(f"✅ Weights updated automatically{surface_info} - version {self.current_weights.version}")

    def _log_weight_change(self, old_weights: Dict[str, Any],
                          new_weights: Dict[str, float],
                          validation_result: Dict[str, Any]):
        """Log weight changes (database logging removed - using JSON storage)"""
        # Note: Database logging removed for consistency with new architecture
        pass

    def get_learning_status(self) -> Dict[str, Any]:
        """Get current learning system status with enhanced surface-specific information"""
        patterns = self.analyze_prediction_patterns()

        # Get surface-specific momentum status
        surface_momentum_status = {}
        for surface in ['Clay', 'Hard', 'Grass']:
            surface_momentum_status[surface] = self.should_optimize_momentum_weights(surface)

        # Get performance analysis over time
        performance_analysis = self.analyze_performance_over_time()

        return {
            'current_weights': self.current_weights.to_dict(),
            'surface_weights': {
                'clay': self.surface_weights.clay_weights.to_dict(),
                'hard': self.surface_weights.hard_weights.to_dict(),
                'grass': self.surface_weights.grass_weights.to_dict()
            },
            'learning_metrics': asdict(self.learning_metrics),
            'prediction_patterns': patterns,
            'surface_momentum_status': surface_momentum_status,
            'performance_analysis': performance_analysis,
            'system_status': {
                'ready_for_learning': patterns.get('sample_size', 0) >= self.min_sample_size,
                'last_optimization': self.current_weights.created_at,
                'weight_version': self.current_weights.version,
                'conservative_mode': True,  # Indicate we're using conservative settings
                'surface_specific_enabled': True
            },
            'learning_parameters': {
                'significance_threshold': self.significance_threshold,
                'max_weight_change': self.max_weight_change,
                'learning_rate': self.learning_rate,
                'surface_sensitivity': self.surface_sensitivity,
                'weight_type_sensitivity': self.weight_type_sensitivity
            }
        }

    def record_prediction_outcome(self, prediction_record: PredictionRecord):
        """Record a prediction outcome for learning"""
        if not prediction_record.is_ai_prediction or not prediction_record.actual_winner:
            return

        # Check if prediction is eligible for learning (must be from completed match)
        if not self._is_prediction_eligible_for_learning(prediction_record):
            print(f"⏸️ Skipping learning for prediction from {prediction_record.match_status or 'unknown'} match")
            return

        # Update learning metrics
        self.learning_metrics.total_predictions += 1
        if prediction_record.predicted_winner == prediction_record.actual_winner:
            self.learning_metrics.correct_predictions += 1

        # Recalculate accuracy
        if self.learning_metrics.total_predictions > 0:
            new_accuracy = (self.learning_metrics.correct_predictions /
                          self.learning_metrics.total_predictions) * 100

            # Update accuracy trend
            self.learning_metrics.accuracy_trend.append(new_accuracy)
            if len(self.learning_metrics.accuracy_trend) > 100:  # Keep last 100 data points
                self.learning_metrics.accuracy_trend = self.learning_metrics.accuracy_trend[-100:]

            # Update best/worst accuracy
            self.learning_metrics.best_accuracy = max(self.learning_metrics.best_accuracy, new_accuracy)
            self.learning_metrics.worst_accuracy = min(self.learning_metrics.worst_accuracy, new_accuracy)

            # Calculate improvement rate
            if len(self.learning_metrics.accuracy_trend) >= 2:
                recent_trend = self.learning_metrics.accuracy_trend[-10:]  # Last 10 predictions
                if len(recent_trend) >= 2:
                    self.learning_metrics.improvement_rate = (recent_trend[-1] - recent_trend[0]) / len(recent_trend)

            self.learning_metrics.accuracy = new_accuracy
            self.learning_metrics.last_updated = datetime.now().isoformat()

        # Save updated metrics
        self.save_metrics()

        # Check if we should trigger optimization
        if (self.learning_metrics.total_predictions % 200 == 0 and  # Every 200 predictions (research-based)
            self.learning_metrics.total_predictions >= self.min_sample_size):
            self.optimize_weights()

    def reset_learning_data(self, confirm: bool = False):
        """Reset all learning data (use with caution)"""
        if not confirm:
            raise ValueError("Must confirm reset by setting confirm=True")

        # Reset metrics
        self.learning_metrics = LearningMetrics()

        # Reset weights to defaults
        self.current_weights = WeightConfiguration()

        # Note: Database operations removed - using JSON-only storage

        # Save reset state
        self.save_configurations()

    def _is_prediction_eligible_for_learning(self, prediction_record: PredictionRecord) -> bool:
        """Check if a prediction is eligible for learning (completed match with outcome)"""
        if not prediction_record.actual_winner:
            return False

        # Only allow learning from completed matches
        if hasattr(prediction_record, 'match_status') and prediction_record.match_status:
            if prediction_record.match_status in ["pending", "draft"]:
                return False

            # Must be from a completed match
            if prediction_record.match_status != "completed":
                return False
        else:
            # If no match status is set, assume it's from an older prediction and allow learning
            # This maintains backward compatibility
            pass

        return True

    def recalculate_all_metrics(self):
        """Recalculate all metrics from existing prediction data"""
        print("🔄 Recalculating all learning metrics from existing data...")

        # Get all completed AI predictions
        completed_predictions = [p for p in self.prediction_tracker.predictions
                               if (p.actual_winner is not None and
                                   p.is_ai_prediction and
                                   self._is_prediction_eligible_for_learning(p))]

        print(f"   Found {len(completed_predictions)} completed AI predictions")

        # Recalculate learning metrics
        self.learning_metrics.total_predictions = len(completed_predictions)
        self.learning_metrics.correct_predictions = sum(1 for p in completed_predictions
                                                       if p.predicted_winner == p.actual_winner)

        if self.learning_metrics.total_predictions > 0:
            accuracy = (self.learning_metrics.correct_predictions /
                       self.learning_metrics.total_predictions) * 100
            self.learning_metrics.accuracy = accuracy

            # Rebuild accuracy trend (simplified)
            self.learning_metrics.accuracy_trend = [accuracy]
            self.learning_metrics.best_accuracy = accuracy
            self.learning_metrics.worst_accuracy = accuracy
            self.learning_metrics.improvement_rate = 0.0
        else:
            self.learning_metrics.accuracy = 0.0
            self.learning_metrics.accuracy_trend = []
            self.learning_metrics.best_accuracy = 0.0
            self.learning_metrics.worst_accuracy = 100.0
            self.learning_metrics.improvement_rate = 0.0

        self.learning_metrics.last_updated = datetime.now().isoformat()

        # Save updated metrics
        self.save_metrics()

        # Update weight configuration metrics
        self._update_weight_configuration_metrics()

        # Save updated configurations
        self.save_configurations()

        print(f"✅ Metrics recalculated:")
        print(f"   Total predictions: {self.learning_metrics.total_predictions}")
        print(f"   Correct predictions: {self.learning_metrics.correct_predictions}")
        print(f"   Accuracy: {self.learning_metrics.accuracy:.1f}%")
        print(f"   Current weights accuracy: {self.current_weights.accuracy_score:.1%}")
        print(f"   Current weights sample size: {self.current_weights.sample_size}")

    def delete_prediction_by_criteria(self, score: tuple, set_number: int, timestamp_str: str, tolerance_seconds: int = 60) -> int:
        """Delete predictions matching the given criteria - Note: Database operations removed, using JSON-only storage"""
        # Note: Database operations removed - using JSON-only storage
        # The adaptive learning system now stores data in JSON files only
        # No database deletion needed as there are no database tables
        print(f"   Note: Adaptive learning system uses JSON-only storage - no database deletion needed")
        return 0
