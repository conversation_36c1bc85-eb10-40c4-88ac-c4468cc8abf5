#!/usr/bin/env python3
"""
Debug enhanced predictions to see why accuracy is 0%
"""

from enhanced_adaptive_learning_system import enhanced_learning_system

def debug_enhanced_predictions():
    """Debug enhanced predictions data format"""
    print("🔍 DEBUGGING ENHANCED PREDICTIONS")
    print("=" * 50)
    
    # Check a few sample predictions to see the data format
    completed_preds = [p for p in enhanced_learning_system.contextual_predictions 
                      if p.actual_winner is not None]
    
    print(f"Total completed predictions: {len(completed_preds)}")
    
    if completed_preds:
        print('\n=== SAMPLE ENHANCED PREDICTIONS ===')
        for i, pred in enumerate(completed_preds[:5]):
            print(f'Prediction {i+1}:')
            print(f'  predicted_winner: "{pred.predicted_winner}" (type: {type(pred.predicted_winner)})')
            print(f'  actual_winner: "{pred.actual_winner}" (type: {type(pred.actual_winner)})')
            print(f'  Match: {pred.predicted_winner == pred.actual_winner}')
            print(f'  was_correct: {getattr(pred, "was_correct", "N/A")}')
            print(f'  prediction_id: {pred.prediction_id}')
            print()
        
        # Check if there's a pattern in the data
        print('\n=== DATA ANALYSIS ===')
        unique_predicted = set(p.predicted_winner for p in completed_preds[:20])
        unique_actual = set(p.actual_winner for p in completed_preds[:20])
        
        print(f"Sample predicted_winner values: {list(unique_predicted)[:10]}")
        print(f"Sample actual_winner values: {list(unique_actual)[:10]}")
        
        # Check if was_correct field exists and is accurate
        has_was_correct = [p for p in completed_preds if hasattr(p, 'was_correct')]
        print(f"Predictions with was_correct field: {len(has_was_correct)}")
        
        if has_was_correct:
            correct_by_field = sum(1 for p in has_was_correct if p.was_correct)
            print(f"Correct by was_correct field: {correct_by_field}")
            
            # Compare with manual calculation
            manual_correct = sum(1 for p in has_was_correct 
                               if p.predicted_winner == p.actual_winner)
            print(f"Correct by manual comparison: {manual_correct}")
    else:
        print("No completed predictions found!")

if __name__ == "__main__":
    debug_enhanced_predictions()
